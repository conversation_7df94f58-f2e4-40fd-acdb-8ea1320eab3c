# Augment Token 无感换号服务

🚀 **智能Token池管理系统，让您的Augment使用体验更加流畅无忧**

📅 **部署时间**: 2025-08-10 16:25 (支持GET请求)

## ✨ 功能特色

- 🔐 **安全可靠**：企业级安全保障，Token加密存储，多重身份验证
- ⚡ **智能分配**：AI驱动的Token分配算法，自动负载均衡
- 🔄 **无感切换**：一键更新Token，无需手动操作
- 📊 **实时监控**：Token健康监控，使用统计，自动预警
- 🔌 **完美兼容**：与现有VSCode插件100%兼容

## 🏗️ 技术架构

- **前端**：Next.js 14 + TypeScript + Tailwind CSS
- **后端**：Next.js API Routes (Serverless)
- **数据库**：Supabase PostgreSQL
- **部署**：Vercel
- **监控**：实时健康检查 + 使用统计

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install
```

### 2. 环境变量配置

创建 `.env.local` 文件：

```env
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# 应用配置
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# API配置
API_SECRET_KEY=our_api_key_v1_2024
AUGMENT_CLIENT_ID=your_augment_client_id
AUGMENT_CLIENT_SECRET=your_augment_client_secret
```

### 3. 数据库设置

1. 在Supabase中创建新项目
2. 执行 `database/schema.sql` 创建表结构
3. 执行 `database/functions.sql` 创建数据库函数

### 4. 本地开发

```bash
# 启动开发服务器
npm run dev

# 访问应用
open http://localhost:3000
```

## 🔌 VSCode插件集成

### 插件代码修改

只需修改一行代码即可切换到我们的服务：

```javascript
// 在 token-manager-simple.js 中
// 原来的：
const DEFAULT_API_BASE_URL = 'https://augmenttoken.159email.shop';

// 改为：
const DEFAULT_API_BASE_URL = 'https://your-domain.vercel.app';
```

### 使用流程

1. 在网站上登录获取用户Token
2. 在VSCode插件中输入用户Token
3. 点击"一键更新"即可使用我们的Token池

## 🚀 部署到Vercel

```bash
# 安装Vercel CLI
npm i -g vercel

# 登录并部署
vercel login
vercel --prod
```

## 📊 API接口

- `POST /api/login` - 用户登录
- `POST /api/user/verify` - 用户验证（兼容插件）
- `GET /api/user/available-tokens` - 获取Token列表（兼容插件）
- `PUT /api/external/v1/tokens` - 更新Token（兼容插件）
- `GET /api/auth` - OAuth授权
- `POST /api/callback` - OAuth回调
- `GET /api/stats` - 统计信息
- `POST /api/health-check` - 健康检查

## 💰 成本优势

- **免费额度**：Vercel + Supabase 免费版足够初期使用
- **低成本扩展**：付费版本仅需$45/月支持大量用户
- **高利润率**：订阅制收入模式，利润率95%+

## ⚠️ 免责声明

本服务仅供学习和研究使用，请遵守相关法律法规。
