-- Supabase数据库函数
-- 用于原子操作和复杂业务逻辑

-- 增加Token使用计数的原子函数
CREATE OR REPLACE FUNCTION increment_usage_count(token_id UUID)
RETURNS INTEGER AS $$
DECLARE
    new_count INTEGER;
BEGIN
    UPDATE token_pool 
    SET usage_count = usage_count + 1,
        last_used_at = NOW(),
        updated_at = NOW()
    WHERE id = token_id
    RETURNING usage_count INTO new_count;
    
    RETURN COALESCE(new_count, 0);
END;
$$ LANGUAGE plpgsql;

-- 获取用户今日使用量
CREATE OR REPLACE FUNCTION get_user_daily_usage(user_id UUID, target_date DATE DEFAULT CURRENT_DATE)
RETURNS INTEGER AS $$
DECLARE
    total_usage INTEGER;
BEGIN
    SELECT COALESCE(SUM(request_count), 0)
    INTO total_usage
    FROM token_usage
    WHERE token_usage.user_id = get_user_daily_usage.user_id
    AND DATE(used_at) = target_date;
    
    RETURN total_usage;
END;
$$ LANGUAGE plpgsql;

-- 获取Token今日使用量
CREATE OR REPLACE FUNCTION get_token_daily_usage(token_id UUID, target_date DATE DEFAULT CURRENT_DATE)
RETURNS INTEGER AS $$
DECLARE
    total_usage INTEGER;
BEGIN
    SELECT COALESCE(SUM(request_count), 0)
    INTO total_usage
    FROM token_usage
    WHERE token_usage.token_id = get_token_daily_usage.token_id
    AND DATE(used_at) = target_date;
    
    RETURN total_usage;
END;
$$ LANGUAGE plpgsql;

-- 清理过期的Token使用记录（保留30天）
CREATE OR REPLACE FUNCTION cleanup_old_usage_records()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM token_usage
    WHERE used_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 清理过期的Token健康检查日志（保留7天）
CREATE OR REPLACE FUNCTION cleanup_old_health_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM token_health_logs
    WHERE checked_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 获取Token池统计信息
CREATE OR REPLACE FUNCTION get_token_pool_stats()
RETURNS TABLE(
    total_tokens INTEGER,
    healthy_tokens INTEGER,
    unhealthy_tokens INTEGER,
    total_usage BIGINT,
    avg_usage_per_token NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_tokens,
        COUNT(*) FILTER (WHERE is_healthy = true)::INTEGER as healthy_tokens,
        COUNT(*) FILTER (WHERE is_healthy = false)::INTEGER as unhealthy_tokens,
        COALESCE(SUM(usage_count), 0) as total_usage,
        COALESCE(AVG(usage_count), 0) as avg_usage_per_token
    FROM token_pool;
END;
$$ LANGUAGE plpgsql;

-- 获取用户统计信息
CREATE OR REPLACE FUNCTION get_user_stats(user_id UUID)
RETURNS TABLE(
    total_requests BIGINT,
    tokens_used INTEGER,
    last_activity TIMESTAMP WITH TIME ZONE,
    daily_usage_today INTEGER,
    daily_limit INTEGER,
    subscription_type TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(tu.request_count), 0) as total_requests,
        COUNT(DISTINCT tu.token_id)::INTEGER as tokens_used,
        MAX(tu.used_at) as last_activity,
        get_user_daily_usage(user_id) as daily_usage_today,
        CASE u.subscription_type
            WHEN 'free' THEN 10
            WHEN 'pro' THEN 1000
            WHEN 'enterprise' THEN -1
        END as daily_limit,
        u.subscription_type::TEXT
    FROM users u
    LEFT JOIN token_usage tu ON u.id = tu.user_id
    WHERE u.id = user_id
    GROUP BY u.id, u.subscription_type;
END;
$$ LANGUAGE plpgsql;

-- 标记Token为不健康
CREATE OR REPLACE FUNCTION mark_token_unhealthy(
    token_id UUID,
    error_msg TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- 更新Token状态
    UPDATE token_pool 
    SET is_healthy = false,
        error_count = error_count + 1,
        updated_at = NOW()
    WHERE id = token_id;
    
    -- 记录健康检查日志
    INSERT INTO token_health_logs (token_id, is_healthy, error_message)
    VALUES (token_id, false, error_msg);
    
    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 标记Token为健康
CREATE OR REPLACE FUNCTION mark_token_healthy(
    token_id UUID,
    response_time_ms INTEGER DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- 更新Token状态
    UPDATE token_pool 
    SET is_healthy = true,
        error_count = 0,
        health_check_at = NOW(),
        updated_at = NOW()
    WHERE id = token_id;
    
    -- 记录健康检查日志
    INSERT INTO token_health_logs (token_id, is_healthy, response_time)
    VALUES (token_id, true, response_time_ms);
    
    RETURN true;
END;
$$ LANGUAGE plpgsql;

-- 获取最少使用的健康Token
CREATE OR REPLACE FUNCTION get_least_used_token()
RETURNS TABLE(
    id UUID,
    access_token TEXT,
    tenant_url VARCHAR(255),
    usage_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tp.id,
        tp.access_token,
        tp.tenant_url,
        tp.usage_count
    FROM token_pool tp
    WHERE tp.is_healthy = true
    AND get_token_daily_usage(tp.id) < tp.max_daily_usage
    ORDER BY tp.usage_count ASC, tp.last_used_at ASC NULLS FIRST
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 批量更新Token健康状态
CREATE OR REPLACE FUNCTION batch_update_token_health(
    token_health_data JSONB
)
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    token_record RECORD;
BEGIN
    -- 遍历传入的Token健康数据
    FOR token_record IN 
        SELECT 
            (value->>'id')::UUID as token_id,
            (value->>'is_healthy')::BOOLEAN as is_healthy,
            (value->>'response_time')::INTEGER as response_time,
            value->>'error_message' as error_message
        FROM jsonb_array_elements(token_health_data)
    LOOP
        -- 更新Token状态
        UPDATE token_pool 
        SET is_healthy = token_record.is_healthy,
            health_check_at = NOW(),
            updated_at = NOW(),
            error_count = CASE 
                WHEN token_record.is_healthy THEN 0 
                ELSE error_count + 1 
            END
        WHERE id = token_record.token_id;
        
        -- 记录健康检查日志
        INSERT INTO token_health_logs (
            token_id, 
            is_healthy, 
            response_time, 
            error_message
        ) VALUES (
            token_record.token_id,
            token_record.is_healthy,
            token_record.response_time,
            token_record.error_message
        );
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;
