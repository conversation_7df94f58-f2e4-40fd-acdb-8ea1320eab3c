-- Augment Token Service 数据库架构
-- 设计用于高性能Token池管理和用户认证

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 创建枚举类型
CREATE TYPE subscription_type AS ENUM ('free', 'pro', 'enterprise');
CREATE TYPE token_source AS ENUM ('oauth', 'manual', 'auto');

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    user_token VARCHAR(255) UNIQUE NOT NULL,
    subscription_type subscription_type DEFAULT 'free',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    total_requests INTEGER DEFAULT 0,
    daily_limit INTEGER DEFAULT 10 -- 免费用户每日限制
);

-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_token ON users(user_token);
CREATE INDEX idx_users_active ON users(is_active) WHERE is_active = true;

-- Token池表
CREATE TABLE token_pool (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    access_token TEXT NOT NULL,
    tenant_url VARCHAR(255) NOT NULL DEFAULT 'https://d5.api.augmentcode.com/',
    refresh_token TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_healthy BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP WITH TIME ZONE,
    usage_count INTEGER DEFAULT 0,
    source token_source DEFAULT 'oauth',
    health_check_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    error_count INTEGER DEFAULT 0,
    max_daily_usage INTEGER DEFAULT 1000, -- 每个Token的日使用限制
    notes TEXT -- 备注信息
);

-- Token池表索引
CREATE INDEX idx_token_pool_healthy ON token_pool(is_healthy) WHERE is_healthy = true;
CREATE INDEX idx_token_pool_usage ON token_pool(usage_count);
CREATE INDEX idx_token_pool_last_used ON token_pool(last_used_at);
CREATE INDEX idx_token_pool_source ON token_pool(source);

-- Token使用记录表
CREATE TABLE token_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_id UUID NOT NULL REFERENCES token_pool(id) ON DELETE CASCADE,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    request_count INTEGER DEFAULT 1,
    session_id VARCHAR(255),
    user_agent TEXT,
    ip_address INET,
    request_type VARCHAR(100), -- 'chat', 'completion', 'edit' 等
    success BOOLEAN DEFAULT true,
    error_message TEXT
);

-- Token使用记录表索引
CREATE INDEX idx_token_usage_user_id ON token_usage(user_id);
CREATE INDEX idx_token_usage_token_id ON token_usage(token_id);
CREATE INDEX idx_token_usage_used_at ON token_usage(used_at);
CREATE INDEX idx_token_usage_session ON token_usage(session_id);

-- 系统配置表
CREATE TABLE system_config (
    key VARCHAR(100) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(255) DEFAULT 'system'
);

-- 系统配置表索引
CREATE INDEX idx_system_config_updated ON system_config(updated_at);

-- Token健康检查日志表
CREATE TABLE token_health_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_id UUID NOT NULL REFERENCES token_pool(id) ON DELETE CASCADE,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_healthy BOOLEAN NOT NULL,
    response_time INTEGER, -- 响应时间（毫秒）
    error_message TEXT,
    http_status INTEGER
);

-- Token健康检查日志索引
CREATE INDEX idx_token_health_logs_token_id ON token_health_logs(token_id);
CREATE INDEX idx_token_health_logs_checked_at ON token_health_logs(checked_at);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_token_pool_updated_at BEFORE UPDATE ON token_pool
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认系统配置
INSERT INTO system_config (key, value, description) VALUES
('token_pool_min_size', '50', 'Token池最小数量'),
('token_pool_max_size', '200', 'Token池最大数量'),
('health_check_interval', '300', 'Token健康检查间隔（秒）'),
('max_requests_per_token_per_day', '1000', '每个Token每日最大请求数'),
('free_user_daily_limit', '10', '免费用户每日请求限制'),
('pro_user_daily_limit', '1000', '专业用户每日请求限制'),
('enterprise_user_daily_limit', '-1', '企业用户每日请求限制（-1表示无限制）'),
('oauth_client_id', '""', 'Augment OAuth客户端ID'),
('oauth_client_secret', '""', 'Augment OAuth客户端密钥'),
('api_version', '1.0.0', 'API版本号');

-- Row Level Security (RLS) 策略
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE token_usage ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的数据
CREATE POLICY "Users can view own data" ON users
    FOR SELECT USING (auth.uid()::text = id);

CREATE POLICY "Users can update own data" ON users
    FOR UPDATE USING (auth.uid()::text = id);

-- Token使用记录的RLS策略
CREATE POLICY "Users can view own usage" ON token_usage
    FOR SELECT USING (user_id = auth.uid()::text);

CREATE POLICY "Users can insert own usage" ON token_usage
    FOR INSERT WITH CHECK (user_id = auth.uid()::text);

-- 创建视图用于统计分析
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.email,
    u.subscription_type,
    u.total_requests,
    COUNT(tu.id) as usage_records,
    MAX(tu.used_at) as last_activity,
    COUNT(DISTINCT tu.token_id) as tokens_used
FROM users u
LEFT JOIN token_usage tu ON u.id = tu.user_id
GROUP BY u.id, u.email, u.subscription_type, u.total_requests;

-- 创建Token健康状态视图
CREATE VIEW token_health_status AS
SELECT 
    tp.id,
    tp.access_token,
    tp.tenant_url,
    tp.is_healthy,
    tp.usage_count,
    tp.last_used_at,
    thl.checked_at as last_health_check,
    thl.response_time,
    thl.error_message
FROM token_pool tp
LEFT JOIN LATERAL (
    SELECT * FROM token_health_logs 
    WHERE token_id = tp.id 
    ORDER BY checked_at DESC 
    LIMIT 1
) thl ON true;
