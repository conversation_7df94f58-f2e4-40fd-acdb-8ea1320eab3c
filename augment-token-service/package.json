{"name": "augment-token-service", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:api": "node scripts/test-api.js", "pre-deploy": "node scripts/pre-deploy-check.js", "deploy": "npm run pre-deploy && vercel --prod", "db:setup": "echo 'Please run database/schema.sql and database/functions.sql in your Supabase SQL editor'", "health-check": "curl -X POST http://localhost:3000/api/health-check -H 'X-API-Key: our_api_key_v1_2024'"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.54.0", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}