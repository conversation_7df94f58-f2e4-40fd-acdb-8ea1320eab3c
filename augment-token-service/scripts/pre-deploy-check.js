/**
 * 部署前检查脚本
 * 验证所有必需的配置和依赖
 */

const fs = require('fs');
const path = require('path');

class PreDeployChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  checkEnvironmentVariables() {
    console.log('🔍 Checking environment variables...');
    
    const requiredEnvVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      'NEXTAUTH_SECRET',
      'API_SECRET_KEY'
    ];

    const optionalEnvVars = [
      'AUGMENT_CLIENT_ID',
      'AUGMENT_CLIENT_SECRET',
      'NEXTAUTH_URL'
    ];

    // 检查必需的环境变量
    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        this.errors.push(`Missing required environment variable: ${envVar}`);
      }
    }

    // 检查可选的环境变量
    for (const envVar of optionalEnvVars) {
      if (!process.env[envVar]) {
        this.warnings.push(`Optional environment variable not set: ${envVar}`);
      }
    }

    if (this.errors.length === 0) {
      console.log('✅ Environment variables check passed');
    }
  }

  checkFileStructure() {
    console.log('🔍 Checking file structure...');
    
    const requiredFiles = [
      'src/app/page.tsx',
      'src/app/login/page.tsx',
      'src/app/dashboard/page.tsx',
      'src/app/api/login/route.ts',
      'src/app/api/user/verify/route.ts',
      'src/app/api/user/available-tokens/route.ts',
      'src/app/api/external/v1/tokens/route.ts',
      'src/app/api/auth/route.ts',
      'src/app/api/callback/route.ts',
      'src/lib/supabase.ts',
      'src/lib/auth-service.ts',
      'src/lib/smart-token-manager.ts',
      'src/lib/token-health-monitor.ts',
      'src/types/index.ts',
      'database/schema.sql',
      'database/functions.sql'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(path.join(process.cwd(), file))) {
        this.errors.push(`Missing required file: ${file}`);
      }
    }

    if (this.errors.length === 0) {
      console.log('✅ File structure check passed');
    }
  }

  checkPackageJson() {
    console.log('🔍 Checking package.json...');
    
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      const requiredDependencies = [
        'next',
        'react',
        'react-dom',
        '@supabase/supabase-js',
        '@supabase/ssr',
        'typescript',
        'tailwindcss'
      ];

      for (const dep of requiredDependencies) {
        if (!packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]) {
          this.errors.push(`Missing required dependency: ${dep}`);
        }
      }

      // 检查脚本
      const requiredScripts = ['dev', 'build', 'start'];
      for (const script of requiredScripts) {
        if (!packageJson.scripts?.[script]) {
          this.errors.push(`Missing required script: ${script}`);
        }
      }

      if (this.errors.length === 0) {
        console.log('✅ Package.json check passed');
      }

    } catch (error) {
      this.errors.push('Failed to read or parse package.json');
    }
  }

  checkDatabaseSchema() {
    console.log('🔍 Checking database schema...');
    
    try {
      const schemaContent = fs.readFileSync('database/schema.sql', 'utf8');
      const functionsContent = fs.readFileSync('database/functions.sql', 'utf8');

      // 检查必需的表
      const requiredTables = ['users', 'token_pool', 'token_usage', 'system_config'];
      for (const table of requiredTables) {
        if (!schemaContent.includes(`CREATE TABLE ${table}`)) {
          this.errors.push(`Missing table definition: ${table}`);
        }
      }

      // 检查必需的函数
      const requiredFunctions = [
        'increment_usage_count',
        'get_user_daily_usage',
        'get_token_daily_usage',
        'get_token_pool_stats'
      ];
      for (const func of requiredFunctions) {
        if (!functionsContent.includes(`CREATE OR REPLACE FUNCTION ${func}`)) {
          this.errors.push(`Missing function definition: ${func}`);
        }
      }

      if (this.errors.length === 0) {
        console.log('✅ Database schema check passed');
      }

    } catch (error) {
      this.errors.push('Failed to read database schema files');
    }
  }

  checkApiCompatibility() {
    console.log('🔍 Checking API compatibility...');
    
    // 检查API路由文件是否存在
    const apiRoutes = [
      'src/app/api/user/verify/route.ts',
      'src/app/api/user/available-tokens/route.ts',
      'src/app/api/external/v1/tokens/route.ts'
    ];

    for (const route of apiRoutes) {
      if (!fs.existsSync(route)) {
        this.errors.push(`Missing API route: ${route}`);
        continue;
      }

      // 检查路由文件内容
      const content = fs.readFileSync(route, 'utf8');
      
      // 检查是否包含必要的导出
      if (!content.includes('export async function')) {
        this.errors.push(`Invalid API route format: ${route}`);
      }

      // 检查是否包含CORS处理
      if (!content.includes('OPTIONS')) {
        this.warnings.push(`Missing CORS handling in: ${route}`);
      }
    }

    if (this.errors.length === 0) {
      console.log('✅ API compatibility check passed');
    }
  }

  async runAllChecks() {
    console.log('🚀 Starting pre-deployment checks...\n');

    this.checkEnvironmentVariables();
    this.checkFileStructure();
    this.checkPackageJson();
    this.checkDatabaseSchema();
    this.checkApiCompatibility();

    console.log('\n📊 Check Results:');
    
    if (this.errors.length > 0) {
      console.log('❌ Errors found:');
      this.errors.forEach(error => console.log(`   - ${error}`));
    }

    if (this.warnings.length > 0) {
      console.log('⚠️  Warnings:');
      this.warnings.forEach(warning => console.log(`   - ${warning}`));
    }

    if (this.errors.length === 0) {
      console.log('✅ All checks passed! Ready for deployment.');
      return true;
    } else {
      console.log(`❌ Found ${this.errors.length} errors. Please fix them before deployment.`);
      return false;
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const checker = new PreDeployChecker();
  checker.runAllChecks().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = PreDeployChecker;
