/**
 * API测试脚本
 * 用于测试所有API接口的功能和兼容性
 */

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const API_KEY = 'our_api_key_v1_2024';

class ApiTester {
  constructor(baseUrl = API_BASE_URL) {
    this.baseUrl = baseUrl;
    this.userToken = null;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
      'User-Agent': 'ApiTester/1.0.0'
    };

    if (this.userToken) {
      defaultHeaders['Authorization'] = `Bearer ${this.userToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers
      }
    });

    const data = await response.json();
    return { response, data };
  }

  async testLogin() {
    console.log('🧪 Testing login API...');
    
    const testEmail = `test_${Date.now()}@example.com`;
    const { response, data } = await this.request('/api/login', {
      method: 'POST',
      body: JSON.stringify({ email: testEmail })
    });

    if (response.ok && data.success) {
      this.userToken = data.token;
      console.log('✅ Login test passed');
      console.log(`   User token: ${this.userToken}`);
      return true;
    } else {
      console.log('❌ Login test failed:', data.error);
      return false;
    }
  }

  async testUserVerify() {
    console.log('🧪 Testing user verify API...');
    
    if (!this.userToken) {
      console.log('❌ No user token available');
      return false;
    }

    const { response, data } = await this.request('/api/user/verify', {
      method: 'POST',
      body: JSON.stringify({ userToken: this.userToken })
    });

    if (response.ok && data.success) {
      console.log('✅ User verify test passed');
      console.log(`   User email: ${data.user.email}`);
      return true;
    } else {
      console.log('❌ User verify test failed:', data.error);
      return false;
    }
  }

  async testAvailableTokens() {
    console.log('🧪 Testing available tokens API...');
    
    if (!this.userToken) {
      console.log('❌ No user token available');
      return false;
    }

    const { response, data } = await this.request('/api/user/available-tokens?page=1&limit=10');

    if (response.ok && data.success) {
      console.log('✅ Available tokens test passed');
      console.log(`   Found ${data.data?.length || 0} tokens`);
      return true;
    } else {
      console.log('❌ Available tokens test failed:', data.error);
      return false;
    }
  }

  async testTokenUpdate() {
    console.log('🧪 Testing token update API...');
    
    if (!this.userToken) {
      console.log('❌ No user token available');
      return false;
    }

    const { response, data } = await this.request('/api/external/v1/tokens', {
      method: 'PUT',
      body: JSON.stringify({ 
        user_ck: this.userToken,
        token: 'test_token_data'
      })
    });

    if (response.ok && data.success) {
      console.log('✅ Token update test passed');
      console.log(`   Message: ${data.message}`);
      return true;
    } else {
      console.log('❌ Token update test failed:', data.error);
      return false;
    }
  }

  async testOAuthAuth() {
    console.log('🧪 Testing OAuth auth API...');
    
    const { response, data } = await this.request('/api/auth');

    if (response.ok && data.authorize_url) {
      console.log('✅ OAuth auth test passed');
      console.log(`   Auth URL generated: ${data.authorize_url.substring(0, 50)}...`);
      return true;
    } else {
      console.log('❌ OAuth auth test failed:', data.error);
      return false;
    }
  }

  async testHealthCheck() {
    console.log('🧪 Testing health check API...');
    
    const { response, data } = await this.request('/api/health-check', {
      method: 'POST'
    });

    if (response.ok && data.success) {
      console.log('✅ Health check test passed');
      console.log(`   Checked ${data.healthCheck.totalChecked} tokens`);
      return true;
    } else {
      console.log('❌ Health check test failed:', data.error);
      return false;
    }
  }

  async testStats() {
    console.log('🧪 Testing stats API...');
    
    if (!this.userToken) {
      console.log('❌ No user token available');
      return false;
    }

    const { response, data } = await this.request('/api/stats');

    if (response.ok) {
      console.log('✅ Stats test passed');
      console.log(`   Token pool: ${data.tokenPool?.healthy}/${data.tokenPool?.total} healthy`);
      return true;
    } else {
      console.log('❌ Stats test failed:', data.error);
      return false;
    }
  }

  async runAllTests() {
    console.log('🚀 Starting API compatibility tests...\n');

    const tests = [
      { name: 'Login', fn: () => this.testLogin() },
      { name: 'User Verify', fn: () => this.testUserVerify() },
      { name: 'Available Tokens', fn: () => this.testAvailableTokens() },
      { name: 'Token Update', fn: () => this.testTokenUpdate() },
      { name: 'OAuth Auth', fn: () => this.testOAuthAuth() },
      { name: 'Health Check', fn: () => this.testHealthCheck() },
      { name: 'Stats', fn: () => this.testStats() }
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      try {
        const result = await test.fn();
        if (result) {
          passed++;
        } else {
          failed++;
        }
      } catch (error) {
        console.log(`❌ ${test.name} test error:`, error.message);
        failed++;
      }
      console.log(''); // 空行分隔
    }

    console.log('📊 Test Results:');
    console.log(`   ✅ Passed: ${passed}`);
    console.log(`   ❌ Failed: ${failed}`);
    console.log(`   📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

    return { passed, failed };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new ApiTester();
  tester.runAllTests().catch(console.error);
}

module.exports = ApiTester;
