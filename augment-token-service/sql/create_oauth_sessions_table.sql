-- 创建OAuth会话表
-- 用于存储PKCE授权流程的临时会话数据

CREATE TABLE IF NOT EXISTS oauth_sessions (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  state VARCHAR(255) NOT NULL UNIQUE,
  code_verifier VARCHAR(255) NOT NULL,
  code_challenge VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_oauth_sessions_state ON oauth_sessions(state);
CREATE INDEX IF NOT EXISTS idx_oauth_sessions_user_expires ON oauth_sessions(user_id, expires_at);
CREATE INDEX IF NOT EXISTS idx_oauth_sessions_expires ON oauth_sessions(expires_at);

-- 设置RLS策略
ALTER TABLE oauth_sessions ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的OAuth会话
CREATE POLICY "Users can only access their own oauth sessions" ON oauth_sessions
  FOR ALL USING (auth.uid() = user_id);

-- 允许服务角色访问所有记录（用于清理过期会话）
CREATE POLICY "Service role can access all oauth sessions" ON oauth_sessions
  FOR ALL USING (auth.role() = 'service_role');

-- 添加注释
COMMENT ON TABLE oauth_sessions IS 'OAuth授权会话临时存储表';
COMMENT ON COLUMN oauth_sessions.user_id IS '用户ID，关联到auth.users表';
COMMENT ON COLUMN oauth_sessions.state IS 'OAuth state参数，用于防CSRF攻击';
COMMENT ON COLUMN oauth_sessions.code_verifier IS 'PKCE code_verifier，用于token交换';
COMMENT ON COLUMN oauth_sessions.code_challenge IS 'PKCE code_challenge，发送给授权服务器';
COMMENT ON COLUMN oauth_sessions.expires_at IS '会话过期时间，通常为30分钟后';
