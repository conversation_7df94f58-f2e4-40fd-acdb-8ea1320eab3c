'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  BookO<PERSON>,
  Copy,
  Key
} from 'lucide-react';
import { useAuth, useAuthRedirect } from '@/hooks/useAuth';
import Navigation, { PageHeader, Message } from '@/components/Navigation';

export default function ApiDocsPage() {
  const [userToken, setUserToken] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [error, setError] = useState('');

  const { user, logout, getUserToken, isAuthenticated } = useAuth();
  const router = useRouter();
  
  useAuthRedirect();

  useEffect(() => {
    if (isAuthenticated) {
      const token = getUserToken();
      setUserToken(token || '');
    }
  }, [isAuthenticated]);

  const copyToClipboard = (text: string, message: string = '已复制到剪贴板') => {
    navigator.clipboard.writeText(text);
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(''), 2000);
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证登录状态...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <Navigation className="mb-8" />
        <PageHeader
          icon={<BookOpen className="w-8 h-8 text-blue-600" />}
          title="📚 API文档"
          description="Augment无感切号 外部接口文档"
          className="mb-8"
        />
        {successMessage && <Message type="success" message={successMessage} className="mb-6" />}
        {error && <Message type="error" message={error} className="mb-6" />}

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Key className="w-5 h-5 mr-2" />
            🔑 您的用户Token
          </h3>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <code className="text-sm text-gray-800 bg-white px-3 py-2 rounded border flex-1 mr-3">
                {userToken}
              </code>
              <button
                onClick={() => copyToClipboard(userToken, '用户Token已复制')}
                className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Copy className="w-4 h-4 mr-2" />
                复制Token
              </button>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              此Token用于调用下方的外部API接口，请妥善保管
            </p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🔐 认证方式</h3>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <p className="text-blue-800 font-medium mb-2">请求头设置</p>
            <div className="space-y-2">
              <div className="flex items-center">
                <code className="bg-white px-2 py-1 rounded text-sm mr-2">Authorization:</code>
                <code className="text-blue-700">Bearer YOUR_USER_TOKEN</code>
              </div>
              <div className="flex items-center">
                <code className="bg-white px-2 py-1 rounded text-sm mr-2">X-API-Key:</code>
                <code className="text-blue-700">our_api_key_v1_2024</code>
              </div>
              <div className="flex items-center">
                <code className="bg-white px-2 py-1 rounded text-sm mr-2">Content-Type:</code>
                <code className="text-blue-700">application/json</code>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mr-3">
                GET
              </span>
              <h3 className="text-lg font-semibold text-gray-900">
                /api/external/auth-url
              </h3>
            </div>
            
            <p className="text-gray-600 mb-6">获取Augment OAuth授权链接</p>

            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">请求参数</h4>
              <div className="overflow-x-auto">
                <table className="w-full border border-gray-200 rounded-lg">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">参数名</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">类型</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">必需</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900 border-b">Authorization</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">Header</td>
                      <td className="px-4 py-3 text-sm text-red-600 border-b">是</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">Bearer {userToken}</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900 border-b">X-API-Key</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">Header</td>
                      <td className="px-4 py-3 text-sm text-red-600 border-b">是</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">our_api_key_v1_2024</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">响应示例</h4>
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre className="text-green-400 text-sm">
{`{
  "success": true,
  "data": {
    "authUrl": "https://api.augmentcode.com/oauth/authorize?client_id=...",
    "expiresIn": 1800,
    "instructions": {
      "step1": "在新标签页中打开授权链接",
      "step2": "完成Augment OAuth授权流程",
      "step3": "复制授权完成后的响应数据",
      "step4": "调用 /api/external/complete-auth 接口提交授权数据"
    }
  }
}`}
                </pre>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">curl 使用示例</h4>
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre className="text-yellow-400 text-sm">
{`curl -X GET "https://your-domain.com/api/external/auth-url" \\
  -H "Authorization: Bearer ${userToken}" \\
  -H "X-API-Key: our_api_key_v1_2024"`}
                </pre>
              </div>
              <button
                onClick={() => copyToClipboard(`curl -X GET "https://your-domain.com/api/external/auth-url" \\
  -H "Authorization: Bearer ${userToken}" \\
  -H "X-API-Key: our_api_key_v1_2024"`, 'curl命令已复制')}
                className="mt-2 flex items-center px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors"
              >
                <Copy className="w-3 h-3 mr-1" />
                复制curl命令
              </button>
            </div>
          </div>

          {/* 完成授权接口 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">
                POST
              </span>
              <h3 className="text-lg font-semibold text-gray-900">
                /api/external/complete-auth
              </h3>
            </div>

            <p className="text-gray-600 mb-6">完成OAuth授权，提交授权响应数据</p>

            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">请求参数</h4>
              <div className="overflow-x-auto">
                <table className="w-full border border-gray-200 rounded-lg">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">参数名</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">类型</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">必需</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 border-b">说明</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900 border-b">Authorization</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">Header</td>
                      <td className="px-4 py-3 text-sm text-red-600 border-b">是</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">Bearer YOUR_TOKEN</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900 border-b">X-API-Key</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">Header</td>
                      <td className="px-4 py-3 text-sm text-red-600 border-b">是</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">our_api_key_v1_2024</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900 border-b">authResponse</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">Body</td>
                      <td className="px-4 py-3 text-sm text-red-600 border-b">是</td>
                      <td className="px-4 py-3 text-sm text-gray-600 border-b">OAuth授权响应对象</td>
                    </tr>
                    <tr>
                      <td className="px-4 py-3 text-sm text-gray-900">authResponse.code</td>
                      <td className="px-4 py-3 text-sm text-gray-600">String</td>
                      <td className="px-4 py-3 text-sm text-red-600">是</td>
                      <td className="px-4 py-3 text-sm text-gray-600">OAuth授权码</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">请求体示例</h4>
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre className="text-green-400 text-sm">
{`{
  "authResponse": {
    "code": "auth_code_from_augment",
    "state": "user_id_timestamp_random",
    "tenant_url": "https://your-tenant.augmentcode.com"
  }
}`}
                </pre>
              </div>
            </div>

            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-3">响应示例</h4>
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre className="text-green-400 text-sm">
{`{
  "success": true,
  "data": {
    "message": "OAuth authorization completed successfully",
    "tokenId": "token_pool_id",
    "tenantUrl": "https://your-tenant.augmentcode.com",
    "createdAt": "2025-08-09T12:00:00.000Z"
  }
}`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
