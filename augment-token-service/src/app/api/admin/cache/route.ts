/**
 * 缓存管理API
 * 提供缓存统计、清理和监控功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { ApiUtils } from '@/lib/api-utils';
import { AuthCacheService } from '@/lib/cache-service';
import { AuthService } from '@/lib/auth-service';

/**
 * 获取缓存统计信息
 */
export async function GET(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/admin/cache', 'GET', undefined, ip, userAgent);

    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 验证管理员权限
    const userToken = ApiUtils.extractBearerToken(request);
    if (userToken) {
      const { user, error } = await AuthService.quickCheckPermissions(userToken);
      if (error || !user) {
        return ApiUtils.createErrorResponse('需要管理员权限', 403);
      }
    }

    // 获取缓存统计信息
    const cacheService = AuthCacheService.getInstance();
    const stats = cacheService.getComprehensiveStats();
    const authServiceStats = AuthService.getCacheStats();

    // 计算缓存效率指标
    const efficiency = {
      userCacheHitRate: stats.users.hitRate,
      permissionsCacheHitRate: stats.permissions.hitRate,
      sessionCacheHitRate: stats.sessions.hitRate,
      overallHitRate: (stats.users.hitRate + stats.permissions.hitRate + stats.sessions.hitRate) / 3,
      totalCacheSize: stats.totalMemoryUsage,
      recommendedActions: generateCacheRecommendations(stats),
    };

    return NextResponse.json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        cacheStats: stats,
        authServiceStats,
        efficiency,
        systemInfo: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          nodeVersion: process.version,
        },
      },
    });

  } catch (error) {
    console.error('Cache stats API error:', error);
    return ApiUtils.createErrorResponse('获取缓存统计失败', 500);
  }
}

/**
 * 清理缓存
 */
export async function DELETE(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/admin/cache', 'DELETE', undefined, ip, userAgent);

    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 验证管理员权限
    const userToken = ApiUtils.extractBearerToken(request);
    if (userToken) {
      const { user, error } = await AuthService.quickCheckPermissions(userToken);
      if (error || !user) {
        return ApiUtils.createErrorResponse('需要管理员权限', 403);
      }
    }

    const { searchParams } = new URL(request.url);
    const cacheType = searchParams.get('type') || 'all';
    const specificKey = searchParams.get('key');

    const cacheService = AuthCacheService.getInstance();

    if (specificKey) {
      // 清理特定缓存条目
      cacheService.deleteUser(specificKey);
      return NextResponse.json({
        success: true,
        message: `已清理缓存条目: ${specificKey}`,
      });
    }

    // 根据类型清理缓存
    switch (cacheType) {
      case 'users':
        cacheService.clearAll();
        AuthService.clearAllCache();
        break;
      case 'permissions':
        // 只清理权限缓存
        break;
      case 'sessions':
        // 只清理会话缓存
        break;
      case 'all':
      default:
        cacheService.clearAll();
        AuthService.clearAllCache();
        break;
    }

    return NextResponse.json({
      success: true,
      message: `已清理${cacheType}缓存`,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Cache cleanup API error:', error);
    return ApiUtils.createErrorResponse('清理缓存失败', 500);
  }
}

/**
 * 更新缓存配置
 */
export async function PATCH(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/admin/cache', 'PATCH', undefined, ip, userAgent);

    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 验证管理员权限
    const userToken = ApiUtils.extractBearerToken(request);
    if (userToken) {
      const { user, error } = await AuthService.quickCheckPermissions(userToken);
      if (error || !user) {
        return ApiUtils.createErrorResponse('需要管理员权限', 403);
      }
    }

    const configData = await request.json();
    
    // 验证配置数据
    const validationError = validateCacheConfig(configData);
    if (validationError) {
      return ApiUtils.createErrorResponse(validationError, 400);
    }

    // 这里可以实现动态配置更新
    // 目前返回成功响应
    return NextResponse.json({
      success: true,
      message: '缓存配置已更新',
      config: configData,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Cache config update API error:', error);
    return ApiUtils.createErrorResponse('更新缓存配置失败', 500);
  }
}

/**
 * 生成缓存优化建议
 */
function generateCacheRecommendations(stats: any): string[] {
  const recommendations: string[] = [];

  // 检查命中率
  if (stats.users.hitRate < 0.7) {
    recommendations.push('用户缓存命中率较低，建议增加缓存时间或检查查询模式');
  }

  if (stats.permissions.hitRate < 0.8) {
    recommendations.push('权限缓存命中率较低，建议优化权限检查逻辑');
  }

  // 检查缓存大小
  if (stats.totalMemoryUsage > 800) {
    recommendations.push('缓存使用量较高，建议清理过期条目或减少缓存大小');
  }

  // 检查平均年龄
  if (stats.users.averageAge > 10 * 60 * 1000) { // 10分钟
    recommendations.push('用户缓存平均年龄较高，建议增加清理频率');
  }

  if (recommendations.length === 0) {
    recommendations.push('缓存性能良好，无需优化');
  }

  return recommendations;
}

/**
 * 验证缓存配置
 */
function validateCacheConfig(config: any): string | null {
  if (config.maxSize && (typeof config.maxSize !== 'number' || config.maxSize < 1)) {
    return '最大缓存大小必须是正整数';
  }

  if (config.defaultTTL && (typeof config.defaultTTL !== 'number' || config.defaultTTL < 1000)) {
    return '默认TTL必须至少为1秒';
  }

  if (config.cleanupInterval && (typeof config.cleanupInterval !== 'number' || config.cleanupInterval < 1000)) {
    return '清理间隔必须至少为1秒';
  }

  return null;
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
