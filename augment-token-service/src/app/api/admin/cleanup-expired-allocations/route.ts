import { NextRequest, NextResponse } from 'next/server';
import { SmartTokenManager } from '@/lib/smart-token-manager';
import { ApiUtils } from '@/lib/api-utils';

/**
 * POST /api/admin/cleanup-expired-allocations
 * 清理过期的Token分配（管理员接口）
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 验证管理员权限（这里可以添加更严格的验证）
    const authHeader = request.headers.get('authorization');
    const apiKey = request.headers.get('x-api-key');
    
    if (!authHeader && !apiKey) {
      return ApiUtils.createErrorResponse('Missing authorization', 401);
    }

    // 2. 执行清理任务
    await SmartTokenManager.releaseExpiredAllocations();

    // 3. 返回成功响应
    return ApiUtils.createSuccessResponse({
      message: 'Expired token allocations cleaned up successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Cleanup expired allocations API error:', error);
    return ApiUtils.createErrorResponse('Internal server error', 500);
  }
}

/**
 * GET /api/admin/cleanup-expired-allocations
 * 获取过期分配统计信息
 */
export async function GET(request: NextRequest) {
  try {
    // 1. 验证管理员权限
    const authHeader = request.headers.get('authorization');
    const apiKey = request.headers.get('x-api-key');
    
    if (!authHeader && !apiKey) {
      return ApiUtils.createErrorResponse('Missing authorization', 401);
    }

    // 2. 获取统计信息
    const stats = await SmartTokenManager.getAllocationStats();

    // 3. 返回统计信息
    return ApiUtils.createSuccessResponse(stats);

  } catch (error) {
    console.error('Get allocation stats API error:', error);
    return ApiUtils.createErrorResponse('Internal server error', 500);
  }
}
