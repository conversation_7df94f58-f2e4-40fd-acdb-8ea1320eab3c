/**
 * 性能测试API
 * 提供系统性能测试和基准测试功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { ApiUtils } from '@/lib/api-utils';
import { AuthService } from '@/lib/auth-service';
import PerformanceTester from '@/lib/performance-tester';

/**
 * 运行性能测试
 */
export async function POST(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/admin/performance-test', 'POST', undefined, ip, userAgent);

    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 验证管理员权限
    const userToken = ApiUtils.extractBearerToken(request);
    if (userToken) {
      const { user, error } = await AuthService.quickCheckPermissions(userToken);
      if (error || !user) {
        return ApiUtils.createErrorResponse('需要管理员权限', 403);
      }
    }

    const { testType, config } = await request.json();

    // 创建性能测试器
    const tester = new PerformanceTester(config);

    let results;

    switch (testType) {
      case 'auth':
        results = await runAuthPerformanceTest(tester, userToken);
        break;
      case 'stats':
        results = await runStatsPerformanceTest(tester, userToken);
        break;
      case 'cache':
        results = await runCachePerformanceTest(tester, userToken);
        break;
      case 'concurrent':
        results = await runConcurrentTest(tester, userToken);
        break;
      case 'full':
        results = await runFullPerformanceTest(tester, userToken);
        break;
      default:
        return ApiUtils.createErrorResponse('不支持的测试类型', 400);
    }

    const report = tester.generateReport();

    return NextResponse.json({
      success: true,
      data: {
        testType,
        results,
        report,
        timestamp: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Performance test API error:', error);
    return ApiUtils.createErrorResponse('性能测试失败', 500);
  }
}

/**
 * 认证性能测试
 */
async function runAuthPerformanceTest(tester: PerformanceTester, userToken: string) {
  const authTest = async () => {
    const response = await fetch('/api/user/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'our_api_key_v1_2024',
      },
      body: JSON.stringify({ userToken }),
    });
    return response.json();
  };

  return await tester.testApiPerformance('用户认证测试', authTest, true);
}

/**
 * 统计API性能测试
 */
async function runStatsPerformanceTest(tester: PerformanceTester, userToken: string) {
  const statsTest = async () => {
    const response = await fetch('/api/stats', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'X-API-Key': 'our_api_key_v1_2024',
      },
    });
    return response.json();
  };

  return await tester.testApiPerformance('统计API测试', statsTest);
}

/**
 * 缓存性能测试
 */
async function runCachePerformanceTest(tester: PerformanceTester, userToken: string) {
  // 先清除缓存
  await fetch('/api/stats?type=all', {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'X-API-Key': 'our_api_key_v1_2024',
    },
  });

  const cacheTest = async () => {
    const response = await fetch('/api/stats', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'X-API-Key': 'our_api_key_v1_2024',
      },
    });
    return response.json();
  };

  // 第一次调用（缓存未命中）
  const coldResult = await tester.testApiPerformance('缓存冷启动测试', cacheTest, false);

  // 第二次调用（缓存命中）
  const warmResult = await tester.testApiPerformance('缓存热启动测试', cacheTest, true);

  return { coldResult, warmResult };
}

/**
 * 并发测试
 */
async function runConcurrentTest(tester: PerformanceTester, userToken: string) {
  const concurrentTest = async () => {
    const response = await fetch('/api/stats', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'X-API-Key': 'our_api_key_v1_2024',
      },
    });
    return response.json();
  };

  return await tester.testConcurrentPerformance('并发访问测试', concurrentTest);
}

/**
 * 完整性能测试
 */
async function runFullPerformanceTest(tester: PerformanceTester, userToken: string) {
  const results = {
    auth: await runAuthPerformanceTest(tester, userToken),
    stats: await runStatsPerformanceTest(tester, userToken),
    cache: await runCachePerformanceTest(tester, userToken),
    concurrent: await runConcurrentTest(tester, userToken),
  };

  return results;
}

/**
 * 获取性能基准数据
 */
export async function GET(request: NextRequest) {
  try {
    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 获取系统性能指标
    const performanceMetrics = await getSystemPerformanceMetrics();

    return NextResponse.json({
      success: true,
      data: performanceMetrics,
    });

  } catch (error) {
    console.error('Performance metrics API error:', error);
    return ApiUtils.createErrorResponse('获取性能指标失败', 500);
  }
}

/**
 * 获取系统性能指标
 */
async function getSystemPerformanceMetrics() {
  const startTime = Date.now();

  // 模拟各种性能指标收集
  const metrics = {
    timestamp: new Date().toISOString(),
    system: {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      nodeVersion: process.version,
    },
    api: {
      averageResponseTime: Math.floor(Math.random() * 200) + 50, // 模拟数据
      requestsPerSecond: Math.floor(Math.random() * 100) + 20,
      errorRate: Math.random() * 0.01,
      successRate: 0.99 + Math.random() * 0.01,
    },
    database: {
      connectionCount: Math.floor(Math.random() * 10) + 5,
      queryTime: Math.floor(Math.random() * 50) + 10,
      cacheHitRate: 0.7 + Math.random() * 0.3,
    },
    cache: {
      hitRate: 0.8 + Math.random() * 0.2,
      size: Math.floor(Math.random() * 1000) + 100,
      evictionRate: Math.random() * 0.1,
    },
    recommendations: generatePerformanceRecommendations(),
  };

  const endTime = Date.now();
  metrics.api.metricsCollectionTime = endTime - startTime;

  return metrics;
}

/**
 * 生成性能建议
 */
function generatePerformanceRecommendations(): string[] {
  const recommendations = [
    '系统运行状态良好',
    '缓存命中率优秀，继续保持',
    'API响应时间在正常范围内',
    '建议定期监控数据库性能',
    '考虑在高峰期增加缓存容量',
  ];

  // 随机返回2-4个建议
  const count = Math.floor(Math.random() * 3) + 2;
  return recommendations.slice(0, count);
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
