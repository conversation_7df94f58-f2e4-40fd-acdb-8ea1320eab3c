/**
 * OAuth授权API接口
 * GET /api/auth - 获取Augment授权链接
 */

import { NextRequest } from 'next/server';
import { ApiUtils } from '@/lib/api-utils';
import type { OAuthAuthResponse } from '@/types';

export async function GET(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/auth', 'GET', undefined, ip, userAgent);

    // 生成OAuth参数
    const state = ApiUtils.generateRandomString(32);
    const codeVerifier = ApiUtils.generateRandomString(128);
    
    // 构建Augment OAuth授权URL
    const authParams = new URLSearchParams({
      response_type: 'code',
      client_id: process.env.AUGMENT_CLIENT_ID || 'default_client_id',
      redirect_uri: `${request.nextUrl.origin}/api/callback`,
      scope: 'openid profile email',
      state: state,
      code_challenge: await generateCodeChallenge(codeVerifier),
      code_challenge_method: 'S256'
    });

    const authorizeUrl = `https://auth.augmentcode.com/oauth2/authorize?${authParams.toString()}`;

    const response: OAuthAuthResponse = {
      authorize_url: authorizeUrl,
      state: state,
      code_verifier: codeVerifier
    };

    // 记录成功访问
    ApiUtils.logApiAccess('/api/auth', 'GET', undefined, ip, userAgent, 200);

    return ApiUtils.createSuccessResponse(response);

  } catch (error) {
    console.error('Auth API error:', error);
    return ApiUtils.createErrorResponse('生成授权链接失败', 500);
  }
}

/**
 * 生成PKCE code challenge
 */
async function generateCodeChallenge(codeVerifier: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(codeVerifier);
  const digest = await crypto.subtle.digest('SHA-256', data);
  
  // 转换为base64url格式
  return btoa(String.fromCharCode(...new Uint8Array(digest)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
