/**
 * OAuth回调处理API接口
 * POST /api/callback - 处理Augment OAuth回调
 */

import { NextRequest } from 'next/server';
import { SmartTokenManager } from '@/lib/smart-token-manager';
import { ApiUtils } from '@/lib/api-utils';
import type { OAuthCallbackRequest, OAuthCallbackResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/callback', 'POST', undefined, ip, userAgent);

    // 解析请求体
    const { data: callbackData, error: parseError } = await ApiUtils.safeParseJSON<OAuthCallbackRequest>(request);
    
    if (parseError || !callbackData) {
      return ApiUtils.createErrorResponse(parseError || '请求数据格式错误', 400);
    }

    // 验证必需字段
    const validationError = ApiUtils.validateRequiredFields(callbackData, ['code', 'state', 'tenant_url']);
    if (validationError) {
      return ApiUtils.createErrorResponse(validationError, 400);
    }

    // 使用授权码换取访问令牌
    const tokenResult = await exchangeCodeForToken(callbackData);
    
    if (!tokenResult.success) {
      return ApiUtils.createErrorResponse(tokenResult.error || 'Token获取失败', 400);
    }

    // 将Token添加到池中
    const addResult = await SmartTokenManager.addTokenToPool(
      tokenResult.accessToken!,
      callbackData.tenant_url,
      'oauth',
      tokenResult.refreshToken
    );

    if (!addResult.success) {
      // 即使添加到池中失败，也返回Token给用户
      console.warn('Failed to add token to pool:', addResult.error);
    }

    // 记录成功访问
    ApiUtils.logApiAccess('/api/callback', 'POST', undefined, ip, userAgent, 200);

    const response: OAuthCallbackResponse = {
      status: 'success',
      token: tokenResult.accessToken,
      tenant_url: callbackData.tenant_url
    };

    return ApiUtils.createSuccessResponse(response);

  } catch (error) {
    console.error('Callback API error:', error);
    return ApiUtils.createErrorResponse('OAuth回调处理失败', 500);
  }
}

/**
 * 使用授权码换取访问令牌
 */
async function exchangeCodeForToken(callbackData: OAuthCallbackRequest): Promise<{
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  error?: string;
}> {
  try {
    // 构建Token请求
    const tokenParams = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: process.env.AUGMENT_CLIENT_ID || 'default_client_id',
      client_secret: process.env.AUGMENT_CLIENT_SECRET || 'default_client_secret',
      code: callbackData.code,
      redirect_uri: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/callback`
    });

    // 发送Token请求
    const tokenResponse = await fetch('https://auth.augmentcode.com/oauth2/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'AugmentTokenService/1.0.0'
      },
      body: tokenParams.toString()
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Token exchange failed:', errorText);
      return {
        success: false,
        error: `Token exchange failed: ${tokenResponse.status}`
      };
    }

    const tokenData = await tokenResponse.json();

    // 验证响应数据
    if (!tokenData.access_token) {
      return {
        success: false,
        error: 'Invalid token response: missing access_token'
      };
    }

    return {
      success: true,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token
    };

  } catch (error) {
    console.error('exchangeCodeForToken error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Token exchange failed'
    };
  }
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
