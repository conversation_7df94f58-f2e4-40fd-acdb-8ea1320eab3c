import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    // 获取最近的OAuth会话记录
    const { data: sessions, error } = await supabase
      .from('oauth_sessions')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        table_exists: false
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        table_exists: true,
        session_count: sessions?.length || 0,
        recent_sessions: sessions?.map(s => ({
          id: s.id,
          user_id: s.user_id,
          state: s.state,
          created_at: s.created_at,
          expires_at: s.expires_at,
          is_expired: new Date(s.expires_at) < new Date()
        })) || []
      }
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      table_exists: false
    });
  }
}
