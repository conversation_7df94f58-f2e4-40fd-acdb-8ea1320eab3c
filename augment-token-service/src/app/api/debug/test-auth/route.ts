import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { ApiUtils } from '@/lib/api-utils';
import { findOAuthSessionByState } from '@/lib/database/oauth-sessions';

export async function POST(request: NextRequest) {
  try {
    console.log('=== Debug Test Auth ===');
    
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key');
    if (!ApiUtils.validateApiKey(apiKey)) {
      return NextResponse.json({ success: false, error: 'Invalid API key' }, { status: 401 });
    }

    // 验证用户Token
    const userToken = request.headers.get('Authorization')?.replace('Bearer ', '');
    const user = await AuthService.verifyUserToken(userToken || '');
    console.log('User validation:', !!user, user?.id);

    if (!user) {
      return NextResponse.json({ success: false, error: 'Invalid user token' }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    console.log('Request body:', body);
    
    const { authResponse } = body;
    if (!authResponse || !authResponse.code || !authResponse.state || !authResponse.tenant_url) {
      return NextResponse.json({ success: false, error: 'Invalid auth response format' }, { status: 400 });
    }

    // 查找OAuth会话
    console.log('Looking for OAuth session with state:', authResponse.state);
    const oauthSession = await findOAuthSessionByState(authResponse.state);
    console.log('OAuth session found:', !!oauthSession);
    
    if (oauthSession) {
      console.log('Session details:', {
        id: oauthSession.id,
        user_id: oauthSession.user_id,
        state: oauthSession.state,
        expires_at: oauthSession.expires_at,
        is_expired: new Date(oauthSession.expires_at) < new Date()
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        user_id: user.id,
        auth_response: authResponse,
        oauth_session_found: !!oauthSession,
        oauth_session: oauthSession ? {
          id: oauthSession.id,
          user_id: oauthSession.user_id,
          state: oauthSession.state,
          expires_at: oauthSession.expires_at,
          is_expired: new Date(oauthSession.expires_at) < new Date()
        } : null
      }
    });

  } catch (error) {
    console.error('Debug test auth error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
