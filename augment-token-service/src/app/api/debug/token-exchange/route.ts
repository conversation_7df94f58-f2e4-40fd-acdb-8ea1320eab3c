import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { code, tenant_url, code_verifier } = body;
    
    console.log('=== Token Exchange Debug ===');
    console.log('Code:', code);
    console.log('Tenant URL:', tenant_url);
    console.log('Code Verifier:', code_verifier?.substring(0, 10) + '...');

    // 尝试多个可能的token endpoint
    const baseUrl = tenant_url.replace(/\/$/, '');
    const possibleEndpoints = [
      `${baseUrl}/oauth/token`,
      `${baseUrl}/auth/token`,
      `${baseUrl}/api/oauth/token`,
      `${baseUrl}/token`
    ];

    const results = [];

    for (const endpoint of possibleEndpoints) {
      console.log('Trying endpoint:', endpoint);
      
      try {
        // 尝试不同的认证方式
        const authMethods = [
          { name: 'Bear<PERSON>', header: `Bearer v` },
          { name: 'Basic', header: `Basic ${Buffer.from('v:').toString('base64')}` },
          { name: 'None', header: null }
        ];

        let bestResponse = null;
        let bestStatus = 999;

        for (const auth of authMethods) {
          const headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
          };

          if (auth.header) {
            headers['Authorization'] = auth.header;
          }

          const response = await fetch(endpoint, {
            method: 'POST',
            headers,
            body: new URLSearchParams({
              grant_type: 'authorization_code',
              client_id: 'v',
              code: code,
              code_verifier: code_verifier,
              tenant_url: tenant_url,
            }),
          });

          // 保存最好的响应（状态码最小的）
          if (response.status < bestStatus) {
            bestStatus = response.status;
            bestResponse = response;
          }

          console.log(`${endpoint} with ${auth.name} auth - Status: ${response.status}`);
        }

        const response = bestResponse;

        const responseText = await response.text();
        console.log(`${endpoint} - Status: ${response.status}, Response: ${responseText}`);
        
        results.push({
          endpoint,
          status: response.status,
          statusText: response.statusText,
          response: responseText,
          headers: Object.fromEntries(response.headers.entries())
        });

        // 如果成功，停止尝试
        if (response.ok) {
          break;
        }

      } catch (error) {
        console.log(`${endpoint} - Error:`, error);
        results.push({
          endpoint,
          error: error instanceof Error ? error.message : 'Network error'
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        input: { code, tenant_url, code_verifier },
        results
      }
    });

  } catch (error) {
    console.error('Token exchange debug error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
