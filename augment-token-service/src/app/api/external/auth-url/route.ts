/**
 * 外部API - 获取OAuth授权链接
 * 供第三方应用调用，获取Augment OAuth授权链接
 */

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { UserService } from '@/lib/database';
import { DatabaseUtils, supabaseAdmin } from '@/lib/supabase';
import { ApiUtils } from '@/lib/api-utils';
import { createOAuthSession } from '@/lib/pkce';
import { createOAuthSessionRecord } from '@/lib/database/oauth-sessions';

export async function GET(request: NextRequest) {
  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key');
    if (!ApiUtils.validateApiKey(apiKey)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid API key',
          code: 'INVALID_API_KEY'
        },
        { status: 401 }
      );
    }

    // 验证用户Token
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    
    if (!userToken) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing user token',
          code: 'MISSING_USER_TOKEN'
        },
        { status: 401 }
      );
    }

    const authResult = await AuthService.verifyUserToken({ userToken });
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid user token',
          code: 'INVALID_USER_TOKEN'
        },
        { status: 401 }
      );
    }

    // 获取用户信息
    const userResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('users')
        .select('id, email, is_active')
        .eq('user_token', userToken)
        .eq('is_active', true)
        .single();
    });

    if (userResult.error || !userResult.data) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        },
        { status: 404 }
      );
    }

    const user = userResult.data;

    // 创建OAuth会话和真实授权链接
    const { session, authUrl } = createOAuthSession(user.id);

    // 将会话信息存储到数据库
    try {
      await createOAuthSessionRecord(session);
    } catch (error) {
      console.error('Failed to create OAuth session record:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Failed to create OAuth session',
          code: 'OAUTH_SESSION_CREATION_FAILED'
        },
        { status: 500 }
      );
    }

    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    ApiUtils.logApiAccess('/api/external/auth-url', 'GET', userToken, ip, userAgent, 200);

    return NextResponse.json({
      success: true,
      data: {
        authUrl,
        expiresIn: 1800, // 30分钟
        instructions: {
          step1: '在新标签页中打开授权链接',
          step2: '完成Augment OAuth授权流程',
          step3: '复制授权完成后的JSON响应数据',
          step4: '调用 /api/external/complete-auth 接口提交授权数据',
        },
      },
    });

  } catch (error) {
    console.error('External auth-url API error:', error);
    
    // 记录API访问（错误）
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    ApiUtils.logApiAccess('/api/external/auth-url', 'GET', userToken, ip, userAgent, 500);

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}



/**
 * 处理CORS预检请求
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
