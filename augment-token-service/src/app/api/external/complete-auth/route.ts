/**
 * 外部API - 完成OAuth授权
 * 处理JSON格式的授权响应，提取并保存Token
 */

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { UserService } from '@/lib/database';
import { DatabaseUtils, supabaseAdmin } from '@/lib/supabase';
import { ApiUtils } from '@/lib/api-utils';
import { validateState } from '@/lib/pkce';
import { findOAuthSessionByState, deleteOAuthSession } from '@/lib/database/oauth-sessions';

interface AuthResponse {
  code: string;
  state: string;
  tenant_url: string;
}

export async function POST(request: NextRequest) {
  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key');
    if (!ApiUtils.validateApiKey(apiKey)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid API key',
          code: 'INVALID_API_KEY'
        },
        { status: 401 }
      );
    }

    // 验证用户Token
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    
    if (!userToken) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing user token',
          code: 'MISSING_USER_TOKEN'
        },
        { status: 401 }
      );
    }

    const authResult = await AuthService.verifyUserToken({ userToken });
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid user token',
          code: 'INVALID_USER_TOKEN'
        },
        { status: 401 }
      );
    }

    // 获取用户信息
    console.log('Looking for user with token:', userToken?.substring(0, 10) + '...');
    const userResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('users')
        .select('id, email, is_active')
        .eq('user_token', userToken)
        .eq('is_active', true)
        .single();
    });

    console.log('User query result:', userResult);

    if (userResult.error || !userResult.data) {
      console.log('User not found, error:', userResult.error);
      return NextResponse.json(
        {
          success: false,
          error: 'User not found',
          code: 'USER_NOT_FOUND',
          debug: {
            userToken: userToken?.substring(0, 10) + '...',
            error: userResult.error
          }
        },
        { status: 404 }
      );
    }

    const user = userResult.data;

    // 解析请求体
    const body = await request.json();
    const authResponse: AuthResponse = body.authResponse || body;

    // 验证授权响应格式
    if (!authResponse.code || !authResponse.state || !authResponse.tenant_url) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid auth response format. Required: {code, state, tenant_url}',
          code: 'INVALID_AUTH_RESPONSE_FORMAT',
          expected: {
            code: 'string - OAuth authorization code',
            state: 'string - State parameter from auth URL',
            tenant_url: 'string - Augment tenant URL'
          }
        },
        { status: 400 }
      );
    }

    // 验证state参数格式
    const stateValidation = validateState(authResponse.state);
    if (!stateValidation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid state parameter format',
          code: 'INVALID_STATE_PARAMETER'
        },
        { status: 400 }
      );
    }

    // 查找OAuth会话记录并验证用户
    console.log('Looking for OAuth session with state:', authResponse.state);
    const oauthSession = await findOAuthSessionByState(authResponse.state);
    console.log('Found OAuth session:', oauthSession ? 'Yes' : 'No');

    if (!oauthSession) {
      return NextResponse.json(
        {
          success: false,
          error: 'OAuth session not found or expired',
          code: 'OAUTH_SESSION_NOT_FOUND'
        },
        { status: 400 }
      );
    }

    // 验证会话属于当前用户
    if (oauthSession.user_id !== parseInt(user.id)) {
      return NextResponse.json(
        {
          success: false,
          error: 'OAuth session does not belong to current user',
          code: 'INVALID_STATE_PARAMETER'
        },
        { status: 400 }
      );
    }

    // 使用授权码和PKCE code_verifier换取访问Token
    console.log('Starting token exchange with code_verifier:', oauthSession.code_verifier.substring(0, 10) + '...');
    const tokenResult = await exchangeCodeForToken(authResponse, oauthSession.code_verifier);

    console.log('Token exchange result:', tokenResult);

    if (!tokenResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: tokenResult.error || 'Failed to exchange code for token',
          code: 'TOKEN_EXCHANGE_FAILED'
        },
        { status: 400 }
      );
    }

    // 保存Token到数据库
    const saveResult = await saveTokenToDatabase(user.id, tokenResult.token!, authResponse.tenant_url);

    if (!saveResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to save token',
          code: 'TOKEN_SAVE_FAILED'
        },
        { status: 500 }
      );
    }

    // 清理使用过的OAuth会话
    try {
      await deleteOAuthSession(authResponse.state);
    } catch (error) {
      console.log('Failed to cleanup OAuth session, but continuing...');
    }

    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    ApiUtils.logApiAccess('/api/external/complete-auth', 'POST', userToken, ip, userAgent, 200);

    return NextResponse.json({
      success: true,
      data: {
        message: 'OAuth authorization completed successfully',
        tokenId: saveResult.tokenId,
        tenantUrl: authResponse.tenant_url,
        createdAt: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('External complete-auth API error:', error);
    
    // 记录API访问（错误）
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    ApiUtils.logApiAccess('/api/external/complete-auth', 'POST', userToken, ip, userAgent, 500);

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

/**
 * 使用授权码和PKCE code_verifier换取访问Token
 * 基于竞争对手成功实现的关键发现进行修改
 */
async function exchangeCodeForToken(authResponse: AuthResponse, codeVerifier: string): Promise<{
  success: boolean;
  token?: string;
  error?: string;
}> {
  try {
    console.log('🚀 === 使用竞争对手成功实现的方法 ===');

    // 使用竞争对手验证成功的参数组合（JSON格式）
    const tokenParams = {
      grant_type: 'authorization_code',
      client_id: 'v', // 与授权时一致
      code: authResponse.code,
      code_verifier: codeVerifier,
      redirect_uri: '', // 关键：必须包含空字符串redirect_uri
    };

    // 使用动态构造的endpoint（竞争对手的方法）
    const tokenUrl = `${authResponse.tenant_url}token`;

    console.log('🔗 Token URL:', tokenUrl);
    console.log('📋 Token Parameters:', tokenParams);

    try {
      const tokenResponse = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json', // 关键：使用JSON格式
          'Accept': 'application/json',
        },
        body: JSON.stringify(tokenParams), // 关键：JSON格式请求体
      });

      const responseText = await tokenResponse.text();
      console.log(`✅ Status: ${tokenResponse.status}`);
      console.log(`📄 Response: ${responseText}`);

      if (tokenResponse.ok) {
        console.log(`🎉 SUCCESS with竞争对手方法!`);

        // 解析token响应
        let tokenData;
        try {
          tokenData = JSON.parse(responseText);
        } catch {
          return {
            success: false,
            error: 'Invalid JSON response from token endpoint',
          };
        }

        console.log('✅ Token data:', tokenData);

        if (!tokenData.access_token) {
          return {
            success: false,
            error: 'No access token in response',
          };
        }

        return {
          success: true,
          token: tokenData.access_token,
        };
      } else {
        // 记录错误详情
        let errorData;
        try {
          errorData = JSON.parse(responseText);
        } catch {
          errorData = { error: responseText };
        }

        console.log('❌ Token exchange failed. Status:', tokenResponse.status);
        console.log('❌ Error response:', errorData);

        // 如果主要方法失败，尝试备用方法
        console.log('🔄 主要方法失败，尝试备用endpoint...');
        return await tryFallbackEndpoints(authResponse, codeVerifier, tokenParams);
      }
    } catch (error) {
      console.log(`❌ Network error with main method:`, error);
      // 如果网络错误，尝试备用方法
      console.log('🔄 网络错误，尝试备用endpoint...');
      return await tryFallbackEndpoints(authResponse, codeVerifier, tokenParams);
    }

  } catch (error) {
    console.error('Token exchange error:', error);
    return {
      success: false,
      error: 'Failed to exchange authorization code',
    };
  }
}

/**
 * 备用endpoint尝试方法
 * 如果主要方法失败，尝试其他可能的endpoint
 */
async function tryFallbackEndpoints(authResponse: AuthResponse, codeVerifier: string, tokenParams: any): Promise<{
  success: boolean;
  token?: string;
  error?: string;
}> {
  // 备用endpoint列表（保留原有的测试endpoint作为备用）
  const fallbackEndpoints = [
    'https://d15.api.augmentcode.com/token',
    'https://d15.api.augmentcode.com/api/token',
    'https://d15.api.augmentcode.com/auth/token',
    'https://d15.api.augmentcode.com/oauth/token',
    'https://d15.api.augmentcode.com/api/auth/token',
    'https://d15.api.augmentcode.com/v1/token',
  ];

  console.log('🔄 === 尝试备用endpoints ===');

  for (let i = 0; i < fallbackEndpoints.length; i++) {
    const endpoint = fallbackEndpoints[i];
    console.log(`\n🔗 === 测试备用endpoint ${i + 1}: ${endpoint} ===`);

    try {
      // 先尝试JSON格式
      let tokenResponse = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(tokenParams),
      });

      let responseText = await tokenResponse.text();
      console.log(`📄 JSON格式响应 - Status: ${tokenResponse.status}, Response: ${responseText}`);

      if (tokenResponse.ok) {
        let tokenData;
        try {
          tokenData = JSON.parse(responseText);
          if (tokenData.access_token) {
            console.log(`🎉 SUCCESS with JSON format at: ${endpoint}!`);
            return {
              success: true,
              token: tokenData.access_token,
            };
          }
        } catch (e) {
          console.log('JSON解析失败，继续尝试...');
        }
      }

      // 如果JSON格式失败，尝试form-encoded格式
      console.log(`🔄 JSON格式失败，尝试form-encoded格式...`);
      tokenResponse = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: new URLSearchParams(tokenParams),
      });

      responseText = await tokenResponse.text();
      console.log(`📄 Form格式响应 - Status: ${tokenResponse.status}, Response: ${responseText}`);

      if (tokenResponse.ok) {
        let tokenData;
        try {
          tokenData = JSON.parse(responseText);
          if (tokenData.access_token) {
            console.log(`🎉 SUCCESS with form-encoded format at: ${endpoint}!`);
            return {
              success: true,
              token: tokenData.access_token,
            };
          }
        } catch (e) {
          console.log('Form格式也失败，继续下一个endpoint...');
        }
      }

    } catch (error) {
      console.log(`❌ Network error with ${endpoint}:`, error);
    }
  }

  return {
    success: false,
    error: 'All endpoints failed including fallback methods',
  };
}

/**
 * 保存Token到数据库
 */
async function saveTokenToDatabase(userId: string, accessToken: string, tenantUrl: string): Promise<{
  success: boolean;
  tokenId?: string;
  error?: string;
}> {
  try {
    // 检查Token是否已存在
    const existingTokenResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_pool')
        .select('id')
        .eq('access_token', accessToken)
        .single();
    });

    if (existingTokenResult.data) {
      // Token已存在，更新使用记录
      const tokenId = existingTokenResult.data.id;
      
      await DatabaseUtils.safeQuery(async () => {
        return await supabaseAdmin!
          .from('token_usage')
          .insert({
            user_id: userId,
            token_id: tokenId,
            used_at: new Date().toISOString(),
            request_count: 1,
            request_type: 'oauth_authorization',
            success: true,
            session_id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          });
      });

      return {
        success: true,
        tokenId,
      };
    }

    // 新Token，添加到Token池
    const insertResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_pool')
        .insert({
          access_token: accessToken,
          tenant_url: tenantUrl,
          source: 'oauth_user_auth',
          is_healthy: true,
          usage_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select('id')
        .single();
    });

    if (insertResult.error || !insertResult.data) {
      return {
        success: false,
        error: 'Failed to save token to pool',
      };
    }

    const tokenId = insertResult.data.id;

    // 记录使用记录
    await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .insert({
          user_id: userId,
          token_id: tokenId,
          used_at: new Date().toISOString(),
          request_count: 1,
          request_type: 'oauth_authorization',
          success: true,
          session_id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        });
    });

    return {
      success: true,
      tokenId,
    };

  } catch (error) {
    console.error('Failed to save token:', error);
    return {
      success: false,
      error: 'Database operation failed',
    };
  }
}

/**
 * 处理CORS预检请求
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
