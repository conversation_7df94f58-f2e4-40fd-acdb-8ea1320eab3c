/**
 * Token更新API接口
 * PUT /api/external/v1/tokens
 * 
 * 与VSCode插件100%兼容的Token更新接口
 */

import { NextRequest } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { SmartTokenManager } from '@/lib/smart-token-manager';
import { ApiUtils } from '@/lib/api-utils';
import type { TokenUpdateRequest } from '@/types';

export async function PUT(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/external/v1/tokens', 'PUT', undefined, ip, userAgent);

    // 验证API Key（兼容现有插件）
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 解析请求体
    const { data: updateData, error: parseError } = await ApiUtils.safeParseJSON<TokenUpdateRequest>(request);
    
    if (parseError || !updateData) {
      return ApiUtils.createErrorResponse(parseError || '请求数据格式错误', 400);
    }

    // 验证必需字段
    const validationError = ApiUtils.validateRequiredFields(updateData, ['user_ck']);
    if (validationError) {
      return ApiUtils.createErrorResponse(validationError, 400);
    }

    // 验证用户权限
    const { user, permissions, error: permError } = await AuthService.checkUserPermissions(updateData.user_ck);
    
    if (permError || !user) {
      return ApiUtils.createErrorResponse(permError || '用户验证失败', 401);
    }

    // 检查用户是否有权限更新Token
    if (!permissions.canAccessTokens) {
      return ApiUtils.createErrorResponse(
        `今日使用次数已达上限 (${permissions.dailyLimit})，请明天再试或升级订阅`, 
        429
      );
    }

    // 分配最优Token
    const { token, error: allocateError } = await SmartTokenManager.allocateOptimalToken(user.id);
    
    if (allocateError || !token) {
      return ApiUtils.createErrorResponse(allocateError || 'Token分配失败', 500);
    }

    // 记录Token使用
    await AuthService.recordUserActivity(user.id, 'token_update', {
      tokenId: token.id,
      ip,
      userAgent
    });

    // 记录成功访问
    ApiUtils.logApiAccess(
      '/api/external/v1/tokens', 
      'PUT', 
      updateData.user_ck, 
      ip, 
      userAgent, 
      200
    );

    // 返回兼容格式的响应
    return ApiUtils.createTokenUpdateResponse(
      true,
      'Token更新成功',
      updateData.user_ck // 返回原始的user_ck
    );

  } catch (error) {
    console.error('Token update API error:', error);
    return ApiUtils.createErrorResponse('服务器内部错误', 500);
  }
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
