/**
 * Token健康检查API接口
 * POST /api/health-check - 手动触发Token健康检查
 */

import { NextRequest } from 'next/server';
import { TokenHealthMonitor } from '@/lib/token-health-monitor';
import { SmartTokenManager } from '@/lib/smart-token-manager';
import { ApiUtils } from '@/lib/api-utils';

export async function POST(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/health-check', 'POST', undefined, ip, userAgent);

    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 可选：验证用户权限（管理员功能）
    const userToken = ApiUtils.extractBearerToken(request);
    if (userToken) {
      // 这里可以添加管理员权限检查
    }

    // 执行健康检查
    console.log('Starting manual health check...');
    const healthResults = await TokenHealthMonitor.checkAllTokensHealth();

    // 执行Token池维护
    const maintenanceResult = await SmartTokenManager.maintainTokenPool();

    // 统计结果
    const healthyCount = healthResults.filter(r => r.is_healthy).length;
    const unhealthyCount = healthResults.filter(r => !r.is_healthy).length;
    const averageResponseTime = healthResults
      .filter(r => r.response_time)
      .reduce((sum, r) => sum + (r.response_time || 0), 0) / healthResults.length || 0;

    const result = {
      success: true,
      healthCheck: {
        totalChecked: healthResults.length,
        healthy: healthyCount,
        unhealthy: unhealthyCount,
        averageResponseTime: Math.round(averageResponseTime),
        timestamp: new Date().toISOString()
      },
      maintenance: {
        cleanedTokens: maintenanceResult.cleaned,
        healthyTokensRemaining: maintenanceResult.healthyCount,
        needsAttention: maintenanceResult.needsAttention
      },
      details: healthResults.map(result => ({
        tokenId: result.token_id,
        isHealthy: result.is_healthy,
        responseTime: result.response_time,
        errorMessage: result.error_message
      }))
    };

    console.log(`Health check completed: ${healthyCount}/${healthResults.length} tokens healthy`);

    // 记录成功访问
    ApiUtils.logApiAccess('/api/health-check', 'POST', userToken || undefined, ip, userAgent, 200);

    return ApiUtils.createSuccessResponse(result);

  } catch (error) {
    console.error('Health check API error:', error);
    return ApiUtils.createErrorResponse('健康检查失败', 500);
  }
}

export async function GET(request: NextRequest) {
  try {
    // 获取最近的健康检查结果
    const stats = await TokenHealthMonitor.getTokenPoolStats();
    
    const result = {
      success: true,
      stats,
      timestamp: new Date().toISOString()
    };

    return ApiUtils.createSuccessResponse(result);

  } catch (error) {
    console.error('Health check status API error:', error);
    return ApiUtils.createErrorResponse('获取健康状态失败', 500);
  }
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
