/**
 * 用户登录API接口
 * POST /api/login
 */

import { NextRequest } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { ApiUtils } from '@/lib/api-utils';
import type { LoginRequest } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/login', 'POST', undefined, ip, userAgent);

    // 解析请求体
    const { data: loginData, error: parseError } = await ApiUtils.safeParseJSON<LoginRequest>(request);
    
    if (parseError || !loginData) {
      return ApiUtils.createErrorResponse(parseError || '请求数据格式错误', 400);
    }

    // 验证必需字段
    const validationError = ApiUtils.validateRequiredFields(loginData, ['email']);
    if (validationError) {
      return ApiUtils.createErrorResponse(validationError, 400);
    }

    // 执行登录
    const loginResult = await AuthService.loginUser(loginData);

    // 记录登录结果
    ApiUtils.logApiAccess(
      '/api/login', 
      'POST', 
      loginResult.token, 
      ip, 
      userAgent, 
      loginResult.success ? 200 : 400
    );

    if (loginResult.success) {
      return ApiUtils.createSuccessResponse(loginResult, 200);
    } else {
      return ApiUtils.createErrorResponse(loginResult.error || '登录失败', 400);
    }

  } catch (error) {
    console.error('Login API error:', error);
    return ApiUtils.createErrorResponse('服务器内部错误', 500);
  }
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
