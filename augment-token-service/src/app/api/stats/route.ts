/**
 * 高性能统计信息API接口
 * GET /api/stats - 获取Token池和用户统计信息
 *
 * 优化特性：
 * - 轻量级用户验证
 * - 多层缓存机制
 * - 优化的数据库查询
 * - 并行数据获取
 * - 智能缓存失效
 */

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { TokenHealthMonitor } from '@/lib/token-health-monitor';
import { SmartTokenManager } from '@/lib/smart-token-manager';
import { ApiUtils } from '@/lib/api-utils';
import { supabaseAdmin } from '@/lib/supabase';
import { HighPerformanceCache } from '@/lib/cache-service';

// 统计数据缓存实例
const statsCache = new HighPerformanceCache({
  maxSize: 100,
  defaultTTL: 2 * 60 * 1000, // 2分钟缓存
  cleanupInterval: 5 * 60 * 1000,
});

// 用户统计缓存（更长的TTL）
const userStatsCache = new HighPerformanceCache({
  maxSize: 500,
  defaultTTL: 5 * 60 * 1000, // 5分钟缓存
  cleanupInterval: 10 * 60 * 1000,
});

// 系统统计缓存（最长的TTL）
const systemStatsCache = new HighPerformanceCache({
  maxSize: 10,
  defaultTTL: 10 * 60 * 1000, // 10分钟缓存
  cleanupInterval: 15 * 60 * 1000,
});

export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);

    ApiUtils.logApiAccess('/api/stats', 'GET', undefined, ip, userAgent);

    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 提取用户Token
    const userToken = ApiUtils.extractBearerToken(request);
    if (!userToken) {
      return ApiUtils.createErrorResponse('未提供认证token', 401);
    }

    // 使用轻量级验证（优化性能）
    const { user, error: authError, fromCache } = await AuthService.quickCheckPermissions(userToken);

    if (authError || !user) {
      return ApiUtils.createErrorResponse(authError || '用户验证失败', 401);
    }

    // 生成缓存键
    const cacheKey = `stats_${user.id}`;
    const tokenPoolCacheKey = 'token_pool_stats';
    const systemCacheKey = 'system_stats';

    // 检查完整统计缓存
    const cachedStats = statsCache.get(cacheKey);
    if (cachedStats) {
      // 记录成功访问（缓存命中）
      ApiUtils.logApiAccess('/api/stats', 'GET', userToken, ip, userAgent, 200);

      return NextResponse.json({
        success: true,
        data: {
          ...cachedStats,
          fromCache: true,
          cacheAge: Date.now() - cachedStats.cacheTimestamp,
          responseTime: Date.now() - startTime,
        }
      });
    }

    // 并行获取统计数据（优化性能）
    const [tokenPoolStats, userStats, systemStats] = await Promise.all([
      getCachedTokenPoolStats(tokenPoolCacheKey),
      getCachedUserStats(user.id),
      getCachedSystemStats(systemCacheKey),
    ]);

    const statsData = {
      user: {
        id: user.id,
        email: user.email,
        subscription_type: user.subscription_type,
        ...userStats
      },
      tokenPool: tokenPoolStats,
      system: systemStats,
      timestamp: new Date().toISOString(),
      cacheTimestamp: Date.now(),
      fromCache: false,
      authFromCache: fromCache,
      responseTime: Date.now() - startTime,
    };

    // 缓存完整统计数据
    statsCache.set(cacheKey, statsData);

    // 记录成功访问
    ApiUtils.logApiAccess('/api/stats', 'GET', userToken, ip, userAgent, 200);

    return NextResponse.json({
      success: true,
      data: statsData
    });

  } catch (error) {
    console.error('Stats API error:', error);
    const responseTime = Date.now() - startTime;

    return NextResponse.json({
      success: false,
      error: '获取统计信息失败',
      details: error instanceof Error ? error.message : 'Unknown error',
      responseTime,
    }, { status: 500 });
  }
}

/**
 * 获取缓存的Token池统计信息
 */
async function getCachedTokenPoolStats(cacheKey: string) {
  // 检查缓存
  const cached = statsCache.get(cacheKey);
  if (cached) {
    return cached;
  }

  // 缓存未命中，查询数据库
  const stats = await getTokenPoolStatsFromDB();

  // 缓存结果（较短的TTL，因为Token状态变化较快）
  statsCache.set(cacheKey, stats, 1 * 60 * 1000); // 1分钟缓存

  return stats;
}

/**
 * 获取缓存的用户统计信息
 */
async function getCachedUserStats(userId: string) {
  const cacheKey = `user_stats_${userId}`;

  // 检查缓存
  const cached = userStatsCache.get(cacheKey);
  if (cached) {
    return cached;
  }

  // 缓存未命中，查询数据库
  const stats = await getUserStats(userId);

  // 缓存结果
  userStatsCache.set(cacheKey, stats);

  return stats;
}

/**
 * 获取缓存的系统统计信息
 */
async function getCachedSystemStats(cacheKey: string) {
  // 检查缓存
  const cached = systemStatsCache.get(cacheKey);
  if (cached) {
    return cached;
  }

  // 缓存未命中，查询数据库
  const stats = await getSystemStats();

  // 缓存结果（最长的TTL，因为系统统计变化较慢）
  systemStatsCache.set(cacheKey, stats);

  return stats;
}

/**
 * 获取用户统计信息（优化版本）
 */
async function getUserStats(userId: string) {
  if (!supabaseAdmin) {
    return getDefaultUserStats();
  }

  try {
    // 优化：使用单个查询获取用户基本信息和统计
    const { data: userInfo, error: userError } = await supabaseAdmin
      .from('users')
      .select('subscription_type, total_requests, daily_limit')
      .eq('id', userId)
      .single();

    if (userError || !userInfo) {
      console.error('Failed to get user info:', userError);
      return getDefaultUserStats();
    }

    // 并行查询今日使用量和最后活动时间
    const [dailyUsageResult, lastActivityResult] = await Promise.all([
      // 获取今日使用量
      supabaseAdmin.rpc('get_user_daily_usage', { user_id: userId }),
      // 获取最后活动时间
      supabaseAdmin
        .from('token_usage')
        .select('used_at')
        .eq('user_id', userId)
        .order('used_at', { ascending: false })
        .limit(1)
        .single()
    ]);

    const dailyUsageToday = dailyUsageResult.data || 0;
    const lastActivity = lastActivityResult.data?.used_at || null;

    return {
      totalRequests: userInfo.total_requests || 0,
      tokensUsed: userInfo.total_requests || 0, // 假设每个请求使用一个token
      lastActivity,
      dailyUsageToday,
      dailyLimit: userInfo.daily_limit || getDailyLimitBySubscription(userInfo.subscription_type),
      subscriptionType: userInfo.subscription_type || 'free'
    };

  } catch (error) {
    console.error('Failed to get user stats:', error);
    return getDefaultUserStats();
  }
}

/**
 * 获取默认用户统计信息
 */
function getDefaultUserStats() {
  return {
    totalRequests: 0,
    tokensUsed: 0,
    lastActivity: null,
    dailyUsageToday: 0,
    dailyLimit: 10,
    subscriptionType: 'free'
  };
}

/**
 * 根据订阅类型获取每日限制
 */
function getDailyLimitBySubscription(subscriptionType: string): number {
  const limits = {
    'free': 10,
    'pro': 1000,
    'enterprise': -1
  };
  return limits[subscriptionType as keyof typeof limits] || 10;
}

/**
 * 获取系统整体统计（优化版本）
 */
async function getSystemStats() {
  if (!supabaseAdmin) {
    return getDefaultSystemStats();
  }

  try {
    const today = new Date().toISOString().split('T')[0];
    const todayStart = `${today}T00:00:00Z`;
    const todayEnd = `${today}T23:59:59Z`;

    // 并行执行所有查询以提高性能
    const [
      totalUsersResult,
      activeUsersTodayResult,
      requestsTodayResult,
      systemHealthResult
    ] = await Promise.all([
      // 获取总用户数
      supabaseAdmin
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true),

      // 获取今日活跃用户数（使用DISTINCT）
      supabaseAdmin
        .from('token_usage')
        .select('user_id', { count: 'exact', head: true })
        .gte('used_at', todayStart)
        .lt('used_at', todayEnd),

      // 获取今日总请求数（使用聚合函数）
      supabaseAdmin.rpc('get_daily_requests_count', {
        target_date: today
      }),

      // 获取系统健康状态
      getSystemHealthMetrics()
    ]);

    const totalUsers = totalUsersResult.count || 0;
    const activeUsersToday = activeUsersTodayResult.count || 0;
    const totalRequestsToday = requestsTodayResult.data || 0;

    return {
      totalUsers,
      activeUsersToday,
      totalRequestsToday,
      systemUptime: systemHealthResult.uptime,
      averageResponseTime: systemHealthResult.averageResponseTime,
      errorRate: systemHealthResult.errorRate,
      cacheHitRate: getCacheHitRate(),
      timestamp: new Date().toISOString(),
    };

  } catch (error) {
    console.error('Failed to get system stats:', error);
    return getDefaultSystemStats();
  }
}

/**
 * 获取默认系统统计信息
 */
function getDefaultSystemStats() {
  return {
    totalUsers: 0,
    activeUsersToday: 0,
    totalRequestsToday: 0,
    systemUptime: '99.9%',
    averageResponseTime: 0,
    errorRate: 0,
    cacheHitRate: 0,
    timestamp: new Date().toISOString(),
  };
}

/**
 * 获取系统健康指标
 */
async function getSystemHealthMetrics() {
  try {
    // 这里可以集成真实的监控数据
    const uptime = process.uptime();
    const uptimePercentage = Math.min(99.99, (uptime / (24 * 60 * 60)) * 100);

    return {
      uptime: `${uptimePercentage.toFixed(2)}%`,
      averageResponseTime: Math.floor(Math.random() * 100) + 50, // 模拟数据
      errorRate: Math.random() * 0.01, // 模拟错误率
    };
  } catch (error) {
    return {
      uptime: '99.9%',
      averageResponseTime: 100,
      errorRate: 0.001,
    };
  }
}

/**
 * 获取缓存命中率
 */
function getCacheHitRate(): number {
  try {
    const statsStats = statsCache.getStats();
    const userStats = userStatsCache.getStats();
    const systemStats = systemStatsCache.getStats();

    const totalRequests = statsStats.totalRequests + userStats.totalRequests + systemStats.totalRequests;
    const totalHits = statsStats.hits + userStats.hits + systemStats.hits;

    return totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;
  } catch (error) {
    return 0;
  }
}

/**
 * 从数据库获取Token池统计（优化版本）
 */
async function getTokenPoolStatsFromDB() {
  if (!supabaseAdmin) {
    console.error('Supabase admin client not available');
    return getDefaultTokenPoolStats();
  }

  try {
    // 并行执行Token池查询和健康日志查询
    const [tokensResult, healthLogsResult] = await Promise.all([
      // 获取Token池基本统计
      supabaseAdmin
        .from('token_pool')
        .select('id, is_healthy, usage_count, last_used_at'),

      // 获取最近的健康检查日志
      supabaseAdmin
        .from('token_health_logs')
        .select('response_time')
        .not('response_time', 'is', null)
        .order('checked_at', { ascending: false })
        .limit(50) // 减少查询数量以提高性能
    ]);

    const { data: tokens, error: tokenError } = tokensResult;
    const { data: healthLogs } = healthLogsResult;

    if (tokenError) {
      console.error('Error querying token_pool:', tokenError);
      return getDefaultTokenPoolStats();
    }

    if (!tokens || tokens.length === 0) {
      console.log('No tokens found in token_pool');
      return getDefaultTokenPoolStats();
    }

    // 高效计算统计数据
    const stats = tokens.reduce((acc, token) => {
      acc.total++;
      if (token.is_healthy) {
        acc.healthy++;
      } else {
        acc.unhealthy++;
      }
      acc.totalUsage += token.usage_count || 0;

      // 跟踪最新活动
      if (token.last_used_at && (!acc.lastActivity || token.last_used_at > acc.lastActivity)) {
        acc.lastActivity = token.last_used_at;
      }

      return acc;
    }, {
      total: 0,
      healthy: 0,
      unhealthy: 0,
      totalUsage: 0,
      lastActivity: null as string | null
    });

    // 计算平均响应时间
    const averageResponseTime = healthLogs && healthLogs.length > 0
      ? Math.round(healthLogs.reduce((sum, log) => sum + (log.response_time || 0), 0) / healthLogs.length)
      : 0;

    const result = {
      ...stats,
      averageResponseTime,
      healthRate: stats.total > 0 ? (stats.healthy / stats.total) * 100 : 0,
      timestamp: new Date().toISOString(),
    };

    console.log('Token pool stats result:', result);
    return result;

  } catch (error) {
    console.error('Failed to get token pool stats:', error);
    return getDefaultTokenPoolStats();
  }
}

/**
 * 获取默认Token池统计信息
 */
function getDefaultTokenPoolStats() {
  return {
    total: 0,
    healthy: 0,
    unhealthy: 0,
    averageResponseTime: 0,
    totalUsage: 0,
    lastActivity: null,
    healthRate: 0,
    timestamp: new Date().toISOString(),
  };
}



/**
 * 清理统计缓存
 */
export async function DELETE(request: NextRequest) {
  try {
    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 验证管理员权限
    const userToken = ApiUtils.extractBearerToken(request);
    if (userToken) {
      const { user, error } = await AuthService.quickCheckPermissions(userToken);
      if (error || !user) {
        return ApiUtils.createErrorResponse('需要管理员权限', 403);
      }
    }

    const { searchParams } = new URL(request.url);
    const cacheType = searchParams.get('type') || 'all';

    // 清理指定类型的缓存
    switch (cacheType) {
      case 'stats':
        statsCache.clear();
        break;
      case 'users':
        userStatsCache.clear();
        break;
      case 'system':
        systemStatsCache.clear();
        break;
      case 'all':
      default:
        statsCache.clear();
        userStatsCache.clear();
        systemStatsCache.clear();
        break;
    }

    return NextResponse.json({
      success: true,
      message: `已清理${cacheType}统计缓存`,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Stats cache cleanup error:', error);
    return ApiUtils.createErrorResponse('清理统计缓存失败', 500);
  }
}

/**
 * 获取统计缓存信息
 */
export async function PATCH(request: NextRequest) {
  try {
    // 验证API Key
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    const cacheInfo = {
      stats: statsCache.getStats(),
      userStats: userStatsCache.getStats(),
      systemStats: systemStatsCache.getStats(),
      totalMemoryUsage: statsCache.size() + userStatsCache.size() + systemStatsCache.size(),
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json({
      success: true,
      data: cacheInfo,
    });

  } catch (error) {
    console.error('Stats cache info error:', error);
    return ApiUtils.createErrorResponse('获取缓存信息失败', 500);
  }
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
