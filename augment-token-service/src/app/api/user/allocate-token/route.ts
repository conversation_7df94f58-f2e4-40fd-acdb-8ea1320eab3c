import { NextRequest, NextResponse } from 'next/server';
import { SmartTokenManager } from '@/lib/smart-token-manager';
import { ApiUtils } from '@/lib/api-utils';

/**
 * POST /api/user/allocate-token
 * 为用户分配一个Token（24小时有效期）
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 验证请求头
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return ApiUtils.createErrorResponse('Missing or invalid authorization header', 401);
    }

    const userToken = authHeader.substring(7);
    if (!userToken) {
      return ApiUtils.createErrorResponse('Missing user token', 401);
    }

    // 2. 解析请求体
    let requestBody;
    try {
      requestBody = await request.json();
    } catch (error) {
      return ApiUtils.createErrorResponse('Invalid JSON in request body', 400);
    }

    const { userId } = requestBody;
    if (!userId) {
      return ApiUtils.createErrorResponse('Missing userId in request body', 400);
    }

    // 3. 分配Token
    const result = await SmartTokenManager.allocateTokenToUser(userId);
    
    if (!result.success) {
      return ApiUtils.createErrorResponse(result.error || 'Failed to allocate token', 500);
    }

    // 4. 返回成功响应
    return ApiUtils.createSuccessResponse({
      message: 'Token allocated successfully',
      token: result.token,
      expiresIn: '24 hours'
    });

  } catch (error) {
    console.error('Allocate token API error:', error);
    return ApiUtils.createErrorResponse('Internal server error', 500);
  }
}

/**
 * GET /api/user/allocate-token
 * 获取用户当前分配的Token信息
 */
export async function GET(request: NextRequest) {
  try {
    // 1. 验证请求头
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return ApiUtils.createErrorResponse('Missing or invalid authorization header', 401);
    }

    const userToken = authHeader.substring(7);
    if (!userToken) {
      return ApiUtils.createErrorResponse('Missing user token', 401);
    }

    // 2. 获取查询参数
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return ApiUtils.createErrorResponse('Missing userId parameter', 400);
    }

    // 3. 查询用户当前分配的Token
    const allocatedTokens = await SmartTokenManager.getUserAllocatedTokens(userId);
    
    // 4. 返回响应
    return ApiUtils.createSuccessResponse({
      allocatedTokens,
      count: allocatedTokens.length
    });

  } catch (error) {
    console.error('Get allocated tokens API error:', error);
    return ApiUtils.createErrorResponse('Internal server error', 500);
  }
}
