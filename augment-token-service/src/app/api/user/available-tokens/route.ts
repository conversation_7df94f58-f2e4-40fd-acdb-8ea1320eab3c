/**
 * 获取可用Token列表API接口
 * GET /api/user/available-tokens
 * 
 * 与VSCode插件100%兼容的Token列表接口
 */

import { NextRequest } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { SmartTokenManager } from '@/lib/smart-token-manager';
import { ApiUtils } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);
    
    ApiUtils.logApiAccess('/api/user/available-tokens', 'GET', undefined, ip, userAgent);

    // 验证API Key（兼容现有插件）
    // 优先检查X-API-Key头，如果没有则跳过验证（兼容竞争对手扩展）
    const apiKey = ApiUtils.extractApiKey(request);
    if (apiKey && !ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 提取Bearer Token
    const userToken = ApiUtils.extractBearerToken(request);
    if (!userToken) {
      return ApiUtils.createErrorResponse('未提供认证token', 401, '/login');
    }

    // 验证用户权限
    const { user, permissions, error: permError } = await AuthService.checkUserPermissions(userToken);
    
    if (permError || !user) {
      return ApiUtils.createErrorResponse(permError || '用户验证失败', 401, '/login');
    }

    // 检查用户是否有权限访问Token
    if (!permissions.canAccessTokens) {
      return ApiUtils.createErrorResponse(
        `今日使用次数已达上限 (${permissions.dailyLimit})，请明天再试或升级订阅`, 
        429
      );
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    // 验证分页参数
    if (page < 1 || limit < 1 || limit > 100) {
      return ApiUtils.createErrorResponse('分页参数无效', 400);
    }

    // 获取可用Token列表
    const tokens = await SmartTokenManager.getHealthyTokensBatch(page, limit, user.id);

    // 记录成功访问
    ApiUtils.logApiAccess(
      '/api/user/available-tokens', 
      'GET', 
      userToken, 
      ip, 
      userAgent, 
      200
    );

    // 返回兼容格式的响应
    return ApiUtils.createAvailableTokensResponse(true, tokens);

  } catch (error) {
    console.error('Available tokens API error:', error);
    return ApiUtils.createErrorResponse('服务器内部错误', 500);
  }
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
