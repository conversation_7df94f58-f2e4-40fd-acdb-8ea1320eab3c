/**
 * 用户详细信息API
 * 获取用户的完整个人资料信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { UserService } from '@/lib/database';
import { DatabaseUtils, supabaseAdmin } from '@/lib/supabase';
import { ApiUtils } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key');
    if (!ApiUtils.validateApiKey(apiKey)) {
      return NextResponse.json(
        { success: false, error: 'Invalid API key' },
        { status: 401 }
      );
    }

    // 验证用户Token
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    
    if (!userToken) {
      return NextResponse.json(
        { success: false, error: 'Missing user token' },
        { status: 401 }
      );
    }

    const authResult = await AuthService.verifyUserToken({ userToken });
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: 'Invalid user token' },
        { status: 401 }
      );
    }

    // 获取用户详细信息
    const userResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('users')
        .select(`
          id,
          email,
          user_token,
          created_at,
          updated_at,
          subscription_type,
          is_active,
          total_requests
        `)
        .eq('user_token', userToken)
        .eq('is_active', true)
        .single();
    });

    if (userResult.error || !userResult.data) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    const userProfile = userResult.data;

    // 获取所有可用Token统计（从token_pool表）
    const tokenPoolResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_pool')
        .select('id, is_healthy, usage_count')
        .eq('is_healthy', true);
    });

    // 获取用户使用统计（从token_usage表）
    const userUsageResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .select('token_id, request_count')
        .eq('user_id', userProfile.id);
    });

    // 计算Token统计
    const availableTokens = tokenPoolResult.data || [];
    const userUsage = userUsageResult.data || [];
    const totalUsage = userUsage.reduce((sum, record) => sum + (record.request_count || 0), 0);

    const tokenStats = {
      totalTokens: availableTokens.length, // 显示所有可用token数量
      activeTokens: availableTokens.filter(token => token.is_healthy).length,
      totalUsage,
    };

    // 获取最近的使用记录
    const recentUsageResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .select('used_at')
        .eq('user_id', userProfile.id)
        .order('used_at', { ascending: false })
        .limit(1)
        .single();
    });

    const lastActivity = recentUsageResult.data?.used_at || null;

    // 构建完整的用户资料
    const profileData = {
      id: userProfile.id,
      email: userProfile.email,
      user_token: userProfile.user_token,
      created_at: userProfile.created_at,
      updated_at: userProfile.updated_at,
      subscription_type: userProfile.subscription_type || 'free',
      is_active: userProfile.is_active,
      total_requests: userProfile.total_requests || 0,

      // 统计信息
      stats: {
        ...tokenStats,
        lastActivity,
      },
    };

    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    ApiUtils.logApiAccess('/api/user/profile', 'GET', userToken, ip, userAgent, 200);

    return NextResponse.json({
      success: true,
      data: profileData,
    });

  } catch (error) {
    console.error('User profile API error:', error);
    
    // 记录API访问（错误）
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    ApiUtils.logApiAccess('/api/user/profile', 'GET', userToken, ip, userAgent, 500);

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * 处理CORS预检请求
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
