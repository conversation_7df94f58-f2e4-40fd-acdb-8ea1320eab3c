/**
 * 用户统计API
 * 获取用户的详细统计信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { UserService } from '@/lib/database';
import { DatabaseUtils, supabaseAdmin } from '@/lib/supabase';
import { ApiUtils } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key');
    if (!ApiUtils.validateApiKey(apiKey)) {
      return NextResponse.json(
        { success: false, error: 'Invalid API key' },
        { status: 401 }
      );
    }

    // 验证用户Token
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    
    if (!userToken) {
      return NextResponse.json(
        { success: false, error: 'Missing user token' },
        { status: 401 }
      );
    }

    const authResult = await AuthService.verifyUserToken({ userToken });
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: 'Invalid user token' },
        { status: 401 }
      );
    }

    // 获取用户信息
    const userResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('users')
        .select('*')
        .eq('user_token', userToken)
        .eq('is_active', true)
        .single();
    });

    if (userResult.error || !userResult.data) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const user = userResult.data;

    // 获取用户总请求数
    const totalRequestsResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .select('request_count')
        .eq('user_id', user.id);
    });

    const totalRequests = totalRequestsResult.data?.reduce(
      (sum, record) => sum + (record.request_count || 0), 
      0
    ) || 0;

    // 获取今日使用量
    const today = new Date().toISOString().split('T')[0];
    const todayUsageResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .select('request_count')
        .eq('user_id', user.id)
        .gte('used_at', `${today}T00:00:00.000Z`)
        .lt('used_at', `${today}T23:59:59.999Z`);
    });

    const todayUsage = todayUsageResult.data?.reduce(
      (sum, record) => sum + (record.request_count || 0), 
      0
    ) || 0;

    // 获取最后活动时间
    const lastActivityResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .select('used_at')
        .eq('user_id', user.id)
        .order('used_at', { ascending: false })
        .limit(1)
        .single();
    });

    const lastActivity = lastActivityResult.data?.used_at || null;

    // 获取使用的Token数量
    const tokensUsedResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .select('token_id')
        .eq('user_id', user.id);
    });

    const uniqueTokens = new Set(
      tokensUsedResult.data?.map(record => record.token_id) || []
    );

    // 构建统计数据
    const stats = {
      email: user.email,
      subscription_type: 'free', // 完全免费
      total_requests: totalRequests,
      daily_limit: -1, // 无限制
      remaining_requests: -1, // 无限制
      today_usage: todayUsage,
      last_activity: lastActivity,
      tokens_used: uniqueTokens.size,
      created_at: user.created_at,
    };

    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    ApiUtils.logApiAccess('/api/user/stats', 'GET', userToken, ip, userAgent, 200);

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error) {
    console.error('User stats API error:', error);
    
    // 记录API访问（错误）
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    ApiUtils.logApiAccess('/api/user/stats', 'GET', userToken, ip, userAgent, 500);

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
