/**
 * 用户Token查询API
 * 获取用户拥有的所有Token，支持分页查询
 */

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { UserService } from '@/lib/database';
import { DatabaseUtils, supabaseAdmin } from '@/lib/supabase';
import { ApiUtils } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key');
    if (!ApiUtils.validateApiKey(apiKey)) {
      return NextResponse.json(
        { success: false, error: 'Invalid API key' },
        { status: 401 }
      );
    }

    // 验证用户Token
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    
    if (!userToken) {
      return NextResponse.json(
        { success: false, error: 'Missing user token' },
        { status: 401 }
      );
    }

    const authResult = await AuthService.verifyUserToken({ userToken });
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: 'Invalid user token' },
        { status: 401 }
      );
    }

    // 获取用户信息
    const userResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('users')
        .select('id')
        .eq('user_token', userToken)
        .eq('is_active', true)
        .single();
    });

    if (userResult.error || !userResult.data) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const userId = userResult.data.id;

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100);
    const offset = (page - 1) * limit;

    console.log('=== USER TOKENS API DEBUG ===');
    console.log('User ID:', userId);
    console.log('Query params - page:', page, 'limit:', limit, 'offset:', offset);

    // 查询所有Token（显示所有token，不只是健康的）
    console.log('Querying token_pool table...');
    const tokensResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_pool')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
    });

    console.log('Tokens query result:', tokensResult);
    console.log('Tokens data:', tokensResult.data);
    console.log('Tokens error:', tokensResult.error);

    if (tokensResult.error) {
      console.error('Failed to fetch tokens:', tokensResult.error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch user tokens' },
        { status: 500 }
      );
    }

    // 获取总Token数量
    console.log('Querying total count...');
    const countResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_pool')
        .select('*', { count: 'exact', head: true });
    });

    console.log('Count query result:', countResult);
    console.log('Count data:', countResult.data);
    console.log('Count error:', countResult.error);
    console.log('Count value:', countResult.count);

    // 如果count查询失败，使用实际数据长度作为备用
    const totalRecords = countResult.count || (tokensResult.data?.length || 0);
    const totalPages = Math.ceil(totalRecords / limit);

    console.log(`Found ${totalRecords} total tokens, ${(tokensResult.data || []).length} on current page`);

    // 处理Token数据
    const processedTokens = (tokensResult.data || []).map(token => ({
      id: token.id,
      token_value: token.access_token,
      token_value_masked: token.access_token
        ? `${token.access_token.slice(0, 8)}${'*'.repeat(20)}${token.access_token.slice(-4)}`
        : '',
      tenant_url: token.tenant_url,
      created_at: token.created_at,
      updated_at: token.updated_at,
      last_used_at: token.last_used_at,
      usage_count: token.usage_count,
      is_active: token.is_healthy,
      expires_at: token.expires_at,
      source: token.source,
      allocated_to: token.allocated_to,      // 🔥 添加分配给谁
      allocated_at: token.allocated_at,      // 🔥 添加分配时间
    }));

    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    ApiUtils.logApiAccess('/api/user/tokens', 'GET', userToken, ip, userAgent, 200);

    return NextResponse.json({
      success: true,
      data: {
        tokens: processedTokens,
        pagination: {
          page,
          limit,
          totalRecords,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        summary: {
          totalTokens: totalRecords,
          activeTokens: processedTokens.filter(token => token.is_active).length,
          inactiveTokens: processedTokens.filter(token => !token.is_active).length,
          totalUsage: processedTokens.reduce((sum, token) => sum + (token.usage_count || 0), 0),
        },
      },
    });

  } catch (error) {
    console.error('User tokens API error:', error);
    
    // 记录API访问（错误）
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    ApiUtils.logApiAccess('/api/user/tokens', 'GET', userToken, ip, userAgent, 500);

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * 处理CORS预检请求
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
    },
  });
}
