/**
 * 用户使用记录API
 * 获取用户的Token使用历史记录
 */

import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { UserService } from '@/lib/database';
import { DatabaseUtils, supabaseAdmin } from '@/lib/supabase';
import { ApiUtils } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key');
    if (!ApiUtils.validateApiKey(apiKey)) {
      return NextResponse.json(
        { success: false, error: 'Invalid API key' },
        { status: 401 }
      );
    }

    // 验证用户Token
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    
    if (!userToken) {
      return NextResponse.json(
        { success: false, error: 'Missing user token' },
        { status: 401 }
      );
    }

    const authResult = await AuthService.verifyUserToken({ userToken });
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { success: false, error: 'Invalid user token' },
        { status: 401 }
      );
    }

    // 获取用户ID - 从数据库查询用户信息
    const userResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('users')
        .select('id')
        .eq('user_token', userToken)
        .eq('is_active', true)
        .single();
    });

    if (userResult.error || !userResult.data) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    const userId = userResult.data.id;

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // 查询使用记录
    const usageResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .select(`
          id,
          used_at,
          request_count,
          request_type,
          success,
          session_id
        `)
        .eq('user_id', userId)
        .order('used_at', { ascending: false })
        .range(offset, offset + limit - 1);
    });

    if (usageResult.error) {
      return NextResponse.json(
        { success: false, error: 'Failed to fetch usage records' },
        { status: 500 }
      );
    }

    // 获取总记录数
    const countResult = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('token_usage')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId);
    });

    const totalRecords = countResult.data?.count || 0;
    const totalPages = Math.ceil(totalRecords / limit);

    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    ApiUtils.logApiAccess('/api/user/usage-history', 'GET', userToken, ip, userAgent, 200);

    return NextResponse.json({
      success: true,
      data: {
        records: usageResult.data || [],
        pagination: {
          page,
          limit,
          totalRecords,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });

  } catch (error) {
    console.error('Usage history API error:', error);
    
    // 记录API访问（错误）
    const ip = ApiUtils.getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');
    ApiUtils.logApiAccess('/api/user/usage-history', 'GET', userToken, ip, userAgent, 500);

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
