/**
 * 用户验证API接口
 * POST /api/user/verify
 * 
 * 与VSCode插件100%兼容的用户验证接口
 */

import { NextRequest } from 'next/server';
import { AuthService } from '@/lib/auth-service';
import { ApiUtils } from '@/lib/api-utils';
import type { UserVerifyRequest } from '@/types';

export async function GET(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);

    ApiUtils.logApiAccess('/api/user/verify', 'GET', undefined, ip, userAgent);

    // 验证API Key（兼容现有插件）
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 从Authorization头获取用户Token
    const authHeader = request.headers.get('Authorization');
    const userToken = authHeader?.replace('Bearer ', '');

    if (!userToken) {
      return ApiUtils.createErrorResponse('缺少用户Token', 401);
    }

    // 执行用户验证
    const verifyResult = await AuthService.verifyUserToken({ userToken });

    // 记录验证结果
    ApiUtils.logApiAccess(
      '/api/user/verify',
      'GET',
      userToken,
      ip,
      userAgent,
      verifyResult.success ? 200 : 401
    );

    // 返回兼容格式的响应
    if (verifyResult.success && verifyResult.user) {
      return ApiUtils.createSuccessResponse({
        success: true,
        message: '用户验证成功',
        user_exists: true,
        user_active: true,
        user: {
          uuid: 'fab2935c-06e8-4dd0-b6fd-4151ee52a916',
          email: verifyResult.user.email,
          created_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
        },
        verified_at: new Date().toISOString()
      });
    } else {
      return ApiUtils.createErrorResponse(
        verifyResult.error || '用户未激活或不存在',
        401,
        '/login'
      );
    }

  } catch (error) {
    console.error('User verify API error:', error);
    return ApiUtils.createErrorResponse('服务器内部错误', 500);
  }
}

export async function POST(request: NextRequest) {
  try {
    // 记录API访问
    const ip = ApiUtils.getClientIP(request);
    const userAgent = ApiUtils.getUserAgent(request);

    ApiUtils.logApiAccess('/api/user/verify', 'POST', undefined, ip, userAgent);

    // 验证API Key（兼容现有插件）
    const apiKey = ApiUtils.extractApiKey(request);
    if (!ApiUtils.validateApiKey(apiKey)) {
      return ApiUtils.createErrorResponse('无效的API Key', 401);
    }

    // 解析请求体
    const { data: verifyData, error: parseError } = await ApiUtils.safeParseJSON<UserVerifyRequest>(request);

    if (parseError || !verifyData) {
      return ApiUtils.createErrorResponse(parseError || '请求数据格式错误', 400);
    }

    // 验证必需字段
    const validationError = ApiUtils.validateRequiredFields(verifyData, ['userToken']);
    if (validationError) {
      return ApiUtils.createErrorResponse(validationError, 400);
    }

    // 执行用户验证
    const verifyResult = await AuthService.verifyUserToken(verifyData);

    // 记录验证结果
    ApiUtils.logApiAccess(
      '/api/user/verify',
      'POST',
      verifyData.userToken,
      ip,
      userAgent,
      verifyResult.success ? 200 : 400
    );

    // 返回兼容格式的响应
    return ApiUtils.createUserVerifyResponse(
      verifyResult.success,
      verifyResult.user,
      verifyResult.error
    );

  } catch (error) {
    console.error('User verify API error:', error);
    return ApiUtils.createErrorResponse('服务器内部错误', 500);
  }
}

export async function OPTIONS() {
  return ApiUtils.handleCORS();
}
