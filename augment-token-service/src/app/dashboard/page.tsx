/**
 * 用户管理面板 - 新版本
 * 参考竞争对手设计，提供更好的OAuth授权体验
 */

'use client';

import { useState, useEffect } from 'react';
import {
  Activity,
  RefreshCw,
  Shield,
  TrendingUp,
  Copy,
  ExternalLink,
  Plus,
  CheckCircle,
  Info
} from 'lucide-react';
import { useAuth, useAuthRedirect } from '@/hooks/useAuth';
import Navigation, { PageHeader, Message } from '@/components/Navigation';
import { PageLoader } from '@/components/ui/LoadingSpinner';
import { DashboardSkeleton } from '@/components/ui/Skeleton';
import { PageTransition, LoadingTransition, NotificationTransition } from '@/components/ui/PageTransition';
import PerformanceMonitor from '@/components/PerformanceMonitor';

interface UserInfo {
  email: string;
  subscription_type: string;
  total_requests: number;
  daily_limit: number;
  remaining_requests: number;
}

interface TokenStats {
  total: number;
  healthy: number;
  unhealthy: number;
}



export default function DashboardPage() {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [tokenStats, setTokenStats] = useState<TokenStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // OAuth授权相关状态
  const [authUrl, setAuthUrl] = useState('');
  const [authResponse, setAuthResponse] = useState('');
  const [isProcessingAuth, setIsProcessingAuth] = useState(false);
  const [isGettingNewToken, setIsGettingNewToken] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);

  const { user, getUserToken, isAuthenticated } = useAuth();

  // 自动重定向逻辑
  useAuthRedirect();

  // 监控tokenStats状态变化
  useEffect(() => {
    console.log('tokenStats state changed:', tokenStats);
  }, [tokenStats]);

  useEffect(() => {
    if (isAuthenticated) {
      loadDashboardData();
    }
  }, [isAuthenticated]);

  // 备用方法：直接加载token统计
  const loadTokenStatsDirectly = async (userToken: string) => {
    try {
      const response = await fetch('/api/user/tokens', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Direct token API response:', data);
        if (data.success && data.data && data.data.tokens) {
          const tokens = data.data.tokens;
          const total = tokens.length;
          const healthy = tokens.filter((t: any) => t.is_active).length;
          const unhealthy = total - healthy;

          setTokenStats({
            total,
            healthy,
            unhealthy
          });
          console.log('Token stats loaded directly:', { total, healthy, unhealthy });
        } else {
          console.error('Invalid token data structure:', data);
          setTokenStats({
            total: 0,
            healthy: 0,
            unhealthy: 0
          });
        }
      }
    } catch (error) {
      console.error('Failed to load token stats directly:', error);
      setTokenStats({
        total: 0,
        healthy: 0,
        unhealthy: 0
      });
    }
  };

  const loadDashboardData = async () => {
    try {
      const userToken = getUserToken();
      if (!userToken) return;

      setIsLoading(true);

      // 加载用户统计信息
      const userResponse = await fetch('/api/user/stats', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.success) {
          setUserInfo(userData.data);
        }
      } else {
        // 如果用户统计API不存在，使用基本信息
        setUserInfo({
          email: user?.email || '',
          subscription_type: 'free',
          total_requests: 0,
          daily_limit: -1, // 完全免费，无限制
          remaining_requests: -1
        });
      }

      // 加载Token池统计
      const statsResponse = await fetch('/api/stats', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      console.log('Stats API response status:', statsResponse.status);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        console.log('Stats API response data:', statsData);

        if (statsData.success) {
          // 检查数据结构，适配不同的API响应格式
          console.log('Raw statsData:', statsData);
          console.log('statsData.data:', statsData.data);
          console.log('statsData.data.tokenPool:', statsData.data?.tokenPool);

          const tokenPoolData = statsData.data?.tokenPool;
          console.log('Extracted tokenPoolData:', tokenPoolData);

          if (tokenPoolData) {
            const newTokenStats = {
              total: tokenPoolData.total || 0,
              healthy: tokenPoolData.healthy || 0,
              unhealthy: tokenPoolData.unhealthy || 0
            };
            console.log('Setting tokenStats with:', newTokenStats);
            setTokenStats(newTokenStats);
          } else {
            console.error('No tokenPool data found in response');
          }

          // 强制验证状态设置
          setTimeout(() => {
            console.log('Checking tokenStats state after 100ms...');
          }, 100);
        } else {
          console.error('Stats API returned success: false', statsData);
        }
      } else {
        console.log('Stats API failed, loading token stats directly');
        // 如果统计API失败，直接查询token列表
        await loadTokenStatsDirectly(userToken);
      }

    } catch (err) {
      setError('加载数据失败，请刷新页面重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 获取OAuth授权链接
  const handleGetNewToken = async () => {
    try {
      const userToken = getUserToken();
      if (!userToken) return;

      // 设置加载状态
      setIsGettingNewToken(true);
      setError('');

      const response = await fetch('/api/external/auth-url', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuthUrl(data.data.authUrl);

          // 自动复制新生成的授权链接
          await copyToClipboard(data.data.authUrl);

          // 显示成功提示
          setSuccessMessage('已经复制授权链接，您可去指纹浏览器访问');
          setTimeout(() => setSuccessMessage(''), 5000); // 5秒后隐藏提示

          setError('');
        } else {
          setError(data.error || '获取授权链接失败');
        }
      }
    } catch (err) {
      setError('获取授权链接失败，请重试');
    } finally {
      setIsGettingNewToken(false);
    }
  };

  // 处理授权响应（JSON格式）
  const handleAuthResponseSubmit = async () => {
    if (!authResponse.trim()) {
      setError('请输入授权响应数据');
      return;
    }

    // 验证JSON格式
    let parsedResponse;
    try {
      parsedResponse = JSON.parse(authResponse);
      if (!parsedResponse.code || !parsedResponse.state || !parsedResponse.tenant_url) {
        setError('授权响应格式错误，必须包含 code、state、tenant_url 字段');
        return;
      }
    } catch (err) {
      setError('授权响应格式错误，请输入有效的JSON格式');
      return;
    }

    setIsProcessingAuth(true);
    setError('');

    try {
      const userToken = getUserToken();
      if (!userToken) return;

      const response = await fetch('/api/external/complete-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
        body: JSON.stringify({ authResponse: parsedResponse }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuthResponse('');
          setSuccessMessage('Token添加成功！系统已自动刷新数据。');
          // 重新加载数据
          await loadDashboardData();

          // 3秒后清除成功消息
          setTimeout(() => setSuccessMessage(''), 3000);
        } else {
          setError(data.error || '处理授权失败');
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        setError(errorData.error || '处理授权失败');
      }
    } catch (err) {
      setError('处理授权失败，请重试');
    } finally {
      setIsProcessingAuth(false);
    }
  };



  // 复制到剪贴板
  const copyToClipboard = async (text: string, showQuickSuccess: boolean = false) => {
    try {
      await navigator.clipboard.writeText(text);
      if (showQuickSuccess) {
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      }
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  if (!isAuthenticated) {
    return (
      <PageTransition type="fade" duration={300}>
        <PageLoader text="正在验证登录状态..." />
      </PageTransition>
    );
  }

  if (isLoading) {
    return (
      <PageTransition type="fade" duration={300}>
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
          <div className="container mx-auto px-4 py-8">
            <Navigation className="mb-8" />
            <DashboardSkeleton />
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition type="fade" duration={400}>
      {/* 顶部通知 - 成功提醒 */}
      <NotificationTransition
        isVisible={!!successMessage}
        position="top"
      >
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-4 shadow-lg mx-4 mt-4">
          <div className="flex items-center">
            <div className="bg-yellow-400 rounded-full p-1 mr-3">
              <CheckCircle className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="font-bold text-lg">🎉 操作成功！</p>
              <p className="text-blue-100">{successMessage}</p>
            </div>
          </div>
        </div>
      </NotificationTransition>

      {/* 顶部通知 - 复制成功 */}
      <NotificationTransition
        isVisible={copySuccess}
        position="top-right"
      >
        <div className="bg-gradient-to-r from-orange-400 to-red-500 text-white rounded-lg p-4 shadow-lg">
          <div className="flex items-center">
            <div className="bg-yellow-300 rounded-full p-1 mr-3">
              <Copy className="w-4 h-4 text-orange-600" />
            </div>
            <span className="font-bold text-lg">📋 复制成功！</span>
          </div>
        </div>
      </NotificationTransition>

      <LoadingTransition
        isLoading={isLoading}
        loadingComponent={
          <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            <div className="container mx-auto px-4 py-8">
              <Navigation className="mb-8" />
              <div className="text-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 text-lg">正在加载用户信息...</p>
              </div>
            </div>
          </div>
        }
      >
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
          <div className="container mx-auto px-4 py-8">
            {/* 导航栏 */}
            <Navigation className="mb-8" />

            {/* 页面标题 */}
            <PageTransition type="slide" direction="down" duration={400} delay={100}>
              <PageHeader
                icon={<Activity className="w-8 h-8 text-blue-600" />}
                title="🔐 Augment OAuth 授权"
                description="Node.js 版本 - 简化的 OAuth 授权流程"
                className="mb-8"
              />
            </PageTransition>

          {/* 消息提示 */}
          {error && (
            <PageTransition type="slide" direction="down" duration={200}>
              <Message type="error" message={error} className="mb-6" />
            </PageTransition>
          )}

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Token池状态 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Token池状态</h3>
              <Shield className="w-6 h-6 text-blue-600" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">总数量：</span>
                <span className="font-medium">{tokenStats?.total || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">健康：</span>
                <span className="font-medium text-green-600">{tokenStats?.healthy || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">异常：</span>
                <span className="font-medium text-red-600">{tokenStats?.unhealthy || 0}</span>
              </div>

            </div>
          </div>

          {/* 用户统计 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">使用统计</h3>
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">总请求：</span>
                <span className="font-medium">{userInfo?.total_requests || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">订阅类型：</span>
                <span className="font-medium text-blue-600">免费版</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">限制：</span>
                <span className="font-medium text-green-600">无限制</span>
              </div>
            </div>
          </div>

          {/* 系统状态 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">系统状态</h3>
              <Activity className="w-6 h-6 text-purple-600" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">服务状态：</span>
                <span className="font-medium text-green-600">正常</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">响应时间：</span>
                <span className="font-medium">&lt; 100ms</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">可用性：</span>
                <span className="font-medium text-green-600">99.9%</span>
              </div>
            </div>
          </div>
        </div>

        {/* OAuth授权区域 - 占满一行 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-semibold text-gray-900">🔐 Augment OAuth 授权</h3>
            <Plus className="w-6 h-6 text-blue-600" />
          </div>

          {/* 左右布局 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左侧：说明和获取链接 */}
            <div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <Info className="w-5 h-5 text-blue-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-blue-800 font-medium mb-2">获取授权说明</p>
                    <ul className="text-blue-700 text-sm space-y-1">
                      <li>• 点击下方按钮获取 Augment OAuth 授权链接</li>
                      <li>• 在新标签页中完成授权流程</li>
                      <li>• 复制授权完成后的JSON响应数据</li>
                      <li>• 粘贴到右侧文本框，系统会自动提取并保存Token</li>
                    </ul>
                  </div>
                </div>
              </div>

              {!authUrl ? (
                <button
                  onClick={handleGetNewToken}
                  disabled={isGettingNewToken}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
                >
                  {isGettingNewToken ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      获取中...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      获取授权链接
                    </>
                  )}
                </button>
              ) : (
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900">步骤1：获取授权链接</h4>
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  </div>

                  <div className="bg-gray-50 rounded-lg p-3 mb-3">
                    <code className="text-sm text-gray-700 block break-all mb-3">
                      {authUrl}
                    </code>

                    <div className="flex space-x-3">
                      <button
                        onClick={() => copyToClipboard(authUrl, true)}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                      >
                        <Copy className="w-4 h-4 mr-2" />
                        复制授权链接
                      </button>
                      <button
                        onClick={() => window.open(authUrl, '_blank')}
                        className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        打开授权页面
                      </button>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-3">
                    点击按钮复制链接或在新标签页中打开进行授权
                  </p>

                  {/* 获取新授权链接按钮 */}
                  <button
                    onClick={handleGetNewToken}
                    disabled={isGettingNewToken}
                    className="w-full bg-gray-100 hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                  >
                    <RefreshCw className={`w-4 h-4 mr-2 ${isGettingNewToken ? 'animate-spin' : ''}`} />
                    {isGettingNewToken ? '获取中...' : '获取新授权链接'}
                  </button>
                </div>
              )}
            </div>

            {/* 右侧：步骤2完成授权 */}
            <div>

                {/* 步骤2：完成授权 */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">步骤2：完成授权</h4>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                    <div className="flex items-start">
                      <Info className="w-4 h-4 text-blue-600 mr-2 mt-0.5" />
                      <div>
                        <p className="text-blue-800 font-medium mb-1">授权响应格式</p>
                        <p className="text-blue-700 text-sm">
                          在授权页面完成操作后，复制JSON格式的响应数据粘贴到下方文本框
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <textarea
                      value={authResponse}
                      onChange={(e) => setAuthResponse(e.target.value)}
                      placeholder='请粘贴授权响应，格式如：{"code":"xxx","state":"xxx","tenant_url":"xxx"}'
                      className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                    />

                    <div className="flex space-x-3">
                      <button
                        onClick={handleAuthResponseSubmit}
                        disabled={!authResponse.trim() || isProcessingAuth}
                        className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                      >
                        {isProcessingAuth ? (
                          <>
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            处理中...
                          </>
                        ) : (
                          <>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            确认授权
                          </>
                        )}
                      </button>

                      <button
                        onClick={() => {
                          setAuthResponse('');
                          setError('');
                        }}
                        className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                      >
                        清空
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 快速操作 - 占满一行 */}
          <div className="bg-white rounded-lg shadow-md p-6 mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>

            {/* 用户Token展示 */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">用户Token</h4>
                <span className="text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">用于登录插件</span>
              </div>
              <div className="bg-gray-50 rounded-lg p-3 mb-3">
                <code className="text-sm text-gray-700 block break-all">
                  {getUserToken() || '暂无Token'}
                </code>
              </div>
              <button
                onClick={() => copyToClipboard(getUserToken() || '', true)}
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center"
              >
                <Copy className="w-4 h-4 mr-2" />
                复制用户Token
              </button>
            </div>

            {/* 其他操作按钮 */}
            <div className="flex flex-wrap gap-4">
              <button
                onClick={loadDashboardData}
                className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                刷新数据
              </button>
            </div>
          </div>

          {/* 性能监控 - 占满一行 */}
          <PageTransition type="slide" direction="up" duration={400} delay={600}>
            <PerformanceMonitor
              className="mt-6"
              autoRefresh={true}
              refreshInterval={10000}
              showDetails={true}
            />
          </PageTransition>
        </div>
      </div>
      </LoadingTransition>
    </PageTransition>
  );
}