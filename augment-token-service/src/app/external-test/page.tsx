/**
 * 外部接口测试页面
 * 参考竞争对手设计，提供完整的4步骤API测试工具
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  TestTube,
  CheckCircle,
  AlertCircle,
  Copy,
  RefreshCw,
  ExternalLink,
  Check,
  Info,
  Key,
  Link,
  Code,
  Zap
} from 'lucide-react';
import { useAuth, useAuthRedirect } from '@/hooks/useAuth';
import Navigation, { PageHeader, Message } from '@/components/Navigation';

interface TestStep {
  id: number;
  title: string;
  description: string;
  completed: boolean;
  enabled: boolean;
  loading: boolean;
  result?: any;
  error?: string;
}

export default function ExternalTestPage() {
  const [userToken, setUserToken] = useState('');
  const [authUrl, setAuthUrl] = useState('');
  const [authResponse, setAuthResponse] = useState('');
  const [testResult, setTestResult] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [error, setError] = useState('');

  // 测试步骤状态
  const [steps, setSteps] = useState<TestStep[]>([
    {
      id: 1,
      title: '配置用户Token',
      description: '验证您的用户Token是否有效',
      completed: false,
      enabled: true,
      loading: false,
    },
    {
      id: 2,
      title: '获取授权链接',
      description: '使用外部API获取OAuth授权链接',
      completed: false,
      enabled: false,
      loading: false,
    },
    {
      id: 3,
      title: '完成授权',
      description: '提交授权响应数据',
      completed: false,
      enabled: false,
      loading: false,
    },
    {
      id: 4,
      title: '验证结果',
      description: '验证授权是否成功',
      completed: false,
      enabled: false,
      loading: false,
    },
  ]);

  const { user, logout, getUserToken, isAuthenticated } = useAuth();
  const router = useRouter();

  // 自动重定向逻辑
  useAuthRedirect();

  useEffect(() => {
    if (isAuthenticated) {
      const token = getUserToken();
      setUserToken(token || '');
    }
  }, [isAuthenticated]);

  // 更新步骤状态
  const updateStep = (stepId: number, updates: Partial<TestStep>) => {
    setSteps(prev => prev.map(step =>
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  // 启用下一步
  const enableNextStep = (currentStepId: number) => {
    setSteps(prev => prev.map(step =>
      step.id === currentStepId + 1 ? { ...step, enabled: true } : step
    ));
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string, message: string = '已复制到剪贴板') => {
    navigator.clipboard.writeText(text);
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(''), 2000);
  };

  // 步骤1：验证用户Token
  const handleVerifyToken = async () => {
    if (!userToken.trim()) {
      setError('请输入用户Token');
      return;
    }

    updateStep(1, { loading: true });
    setError('');

    try {
      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          updateStep(1, {
            loading: false,
            completed: true,
            result: `✅ Token验证成功 用户: ${data.data.email}`
          });
          enableNextStep(1);
          setSuccessMessage('Token验证成功！');
        } else {
          updateStep(1, {
            loading: false,
            error: data.error || 'Token验证失败'
          });
          setError(data.error || 'Token验证失败');
        }
      } else {
        updateStep(1, {
          loading: false,
          error: `HTTP ${response.status}: Token验证失败`
        });
        setError('Token验证失败');
      }
    } catch (err) {
      updateStep(1, {
        loading: false,
        error: '网络错误，请重试'
      });
      setError('网络错误，请重试');
    }
  };

  // 步骤2：获取授权链接
  const handleGetAuthUrl = async () => {
    updateStep(2, { loading: true });
    setError('');

    try {
      const response = await fetch('/api/external/auth-url', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setAuthUrl(data.data.authUrl);
          updateStep(2, {
            loading: false,
            completed: true,
            result: '✅ 授权链接获取成功'
          });
          enableNextStep(2);
          setSuccessMessage('授权链接获取成功！');
        } else {
          updateStep(2, {
            loading: false,
            error: data.error || '获取授权链接失败'
          });
          setError(data.error || '获取授权链接失败');
        }
      } else {
        updateStep(2, {
          loading: false,
          error: `HTTP ${response.status}: 获取授权链接失败`
        });
        setError('获取授权链接失败');
      }
    } catch (err) {
      updateStep(2, {
        loading: false,
        error: '网络错误，请重试'
      });
      setError('网络错误，请重试');
    }
  };

  // 步骤3：完成授权
  const handleCompleteAuth = async () => {
    if (!authResponse.trim()) {
      setError('请输入授权响应数据');
      return;
    }

    let parsedResponse;
    try {
      parsedResponse = JSON.parse(authResponse);
    } catch (err) {
      setError('授权响应格式错误，请输入有效的JSON格式');
      return;
    }

    updateStep(3, { loading: true });
    setError('');

    try {
      const response = await fetch('/api/external/complete-auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
        body: JSON.stringify({ authResponse: parsedResponse }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          updateStep(3, {
            loading: false,
            completed: true,
            result: '✅ 授权完成成功'
          });
          enableNextStep(3);
          setSuccessMessage('授权完成成功！Token已保存到系统');
        } else {
          updateStep(3, {
            loading: false,
            error: data.error || '授权完成失败'
          });
          setError(data.error || '授权完成失败');
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        updateStep(3, {
          loading: false,
          error: errorData.error || `HTTP ${response.status}: 授权完成失败`
        });
        setError(errorData.error || '授权完成失败');
      }
    } catch (err) {
      updateStep(3, {
        loading: false,
        error: '网络错误，请重试'
      });
      setError('网络错误，请重试');
    }
  };

  // 步骤4：验证结果
  const handleVerifyResult = async () => {
    updateStep(4, { loading: true });
    setError('');

    try {
      const response = await fetch('/api/user/tokens?page=1&limit=5', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const tokenCount = data.data.tokens.length;
          setTestResult(`验证成功！您当前拥有 ${tokenCount} 个Token`);
          updateStep(4, {
            loading: false,
            completed: true,
            result: `✅ 验证成功！当前拥有 ${tokenCount} 个Token`
          });
          setSuccessMessage('测试流程全部完成！');
        } else {
          updateStep(4, {
            loading: false,
            error: data.error || '验证失败'
          });
          setError(data.error || '验证失败');
        }
      } else {
        updateStep(4, {
          loading: false,
          error: `HTTP ${response.status}: 验证失败`
        });
        setError('验证失败');
      }
    } catch (err) {
      updateStep(4, {
        loading: false,
        error: '网络错误，请重试'
      });
      setError('网络错误，请重试');
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证登录状态...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 导航栏 */}
        <Navigation className="mb-8" />

        {/* 页面标题 */}
        <PageHeader
          icon={<TestTube className="w-8 h-8 text-purple-600" />}
          title="🧪 外部接口测试"
          description="测试外部API接口的完整流程"
          className="mb-8"
        />

        {/* 消息提示 */}
        {successMessage && <Message type="success" message={successMessage} className="mb-6" />}
        {error && <Message type="error" message={error} className="mb-6" />}

        {/* 测试步骤 */}
        <div className="space-y-6">
          {/* 步骤1：配置用户Token */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${
                  steps[0].completed
                    ? 'bg-green-600 text-white'
                    : steps[0].enabled
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-300 text-gray-600'
                }`}>
                  {steps[0].completed ? <Check className="w-4 h-4" /> : '1'}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{steps[0].title}</h3>
                  <p className="text-gray-600 text-sm">{steps[0].description}</p>
                </div>
              </div>

              {steps[0].completed && (
                <CheckCircle className="w-6 h-6 text-green-600" />
              )}
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  用户Token
                </label>
                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={userToken}
                    onChange={(e) => setUserToken(e.target.value)}
                    placeholder="请输入您的用户Token"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                  />
                  <button
                    onClick={() => copyToClipboard(userToken, '用户Token已复制')}
                    className="px-3 py-2 text-gray-600 hover:text-blue-600 transition-colors"
                    title="复制Token"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {steps[0].result && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <p className="text-green-700 text-sm">{steps[0].result}</p>
                </div>
              )}

              {steps[0].error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-700 text-sm">{steps[0].error}</p>
                </div>
              )}

              <button
                onClick={handleVerifyToken}
                disabled={!steps[0].enabled || steps[0].loading || !userToken.trim()}
                className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {steps[0].loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    验证中...
                  </>
                ) : (
                  <>
                    <Key className="w-4 h-4 mr-2" />
                    验证Token
                  </>
                )}
              </button>
            </div>
          </div>

          {/* 步骤2：获取授权链接 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${
                  steps[1].completed
                    ? 'bg-green-600 text-white'
                    : steps[1].enabled
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-300 text-gray-600'
                }`}>
                  {steps[1].completed ? <Check className="w-4 h-4" /> : '2'}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{steps[1].title}</h3>
                  <p className="text-gray-600 text-sm">{steps[1].description}</p>
                </div>
              </div>

              {steps[1].completed && (
                <CheckCircle className="w-6 h-6 text-green-600" />
              )}
            </div>

            <div className="space-y-4">
              {authUrl && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    授权链接
                  </label>
                  <div className="bg-gray-50 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <code className="text-sm text-gray-700 flex-1 mr-3 break-all">
                        {authUrl}
                      </code>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => copyToClipboard(authUrl, '授权链接已复制')}
                          className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                          title="复制链接"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => window.open(authUrl, '_blank')}
                          className="p-1 text-blue-600 hover:text-blue-700 transition-colors"
                          title="在新标签页打开"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {steps[1].result && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <p className="text-green-700 text-sm">{steps[1].result}</p>
                </div>
              )}

              {steps[1].error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-700 text-sm">{steps[1].error}</p>
                </div>
              )}

              <button
                onClick={handleGetAuthUrl}
                disabled={!steps[1].enabled || steps[1].loading}
                className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {steps[1].loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    获取中...
                  </>
                ) : (
                  <>
                    <Link className="w-4 h-4 mr-2" />
                    获取授权链接
                  </>
                )}
              </button>
            </div>
          </div>

          {/* 步骤3：完成授权 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${
                  steps[2].completed
                    ? 'bg-green-600 text-white'
                    : steps[2].enabled
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-300 text-gray-600'
                }`}>
                  {steps[2].completed ? <Check className="w-4 h-4" /> : '3'}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{steps[2].title}</h3>
                  <p className="text-gray-600 text-sm">{steps[2].description}</p>
                </div>
              </div>

              {steps[2].completed && (
                <CheckCircle className="w-6 h-6 text-green-600" />
              )}
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <Info className="w-5 h-5 text-blue-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-blue-800 font-medium mb-1">授权响应格式</p>
                    <p className="text-blue-700 text-sm">
                      请在授权页面完成操作后，复制JSON格式的响应数据粘贴到下方文本框
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  授权响应 (JSON格式)
                </label>
                <textarea
                  value={authResponse}
                  onChange={(e) => setAuthResponse(e.target.value)}
                  placeholder='请粘贴授权响应，格式如：{"code":"xxx","state":"xxx","tenant_url":"xxx"}'
                  className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none resize-none"
                />
              </div>

              {steps[2].result && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <p className="text-green-700 text-sm">{steps[2].result}</p>
                </div>
              )}

              {steps[2].error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-700 text-sm">{steps[2].error}</p>
                </div>
              )}

              <button
                onClick={handleCompleteAuth}
                disabled={!steps[2].enabled || steps[2].loading || !authResponse.trim()}
                className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {steps[2].loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    处理中...
                  </>
                ) : (
                  <>
                    <Code className="w-4 h-4 mr-2" />
                    完成授权
                  </>
                )}
              </button>
            </div>
          </div>

          {/* 步骤4：验证结果 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${
                  steps[3].completed
                    ? 'bg-green-600 text-white'
                    : steps[3].enabled
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-300 text-gray-600'
                }`}>
                  {steps[3].completed ? <Check className="w-4 h-4" /> : '4'}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{steps[3].title}</h3>
                  <p className="text-gray-600 text-sm">{steps[3].description}</p>
                </div>
              </div>

              {steps[3].completed && (
                <CheckCircle className="w-6 h-6 text-green-600" />
              )}
            </div>

            <div className="space-y-4">
              {testResult && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <p className="text-green-700 font-medium">{testResult}</p>
                </div>
              )}

              {steps[3].result && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <p className="text-green-700 text-sm">{steps[3].result}</p>
                </div>
              )}

              {steps[3].error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-700 text-sm">{steps[3].error}</p>
                </div>
              )}

              <button
                onClick={handleVerifyResult}
                disabled={!steps[3].enabled || steps[3].loading}
                className="flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                {steps[3].loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    验证中...
                  </>
                ) : (
                  <>
                    <Zap className="w-4 h-4 mr-2" />
                    验证结果
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* 完成提示 */}
        {steps.every(step => step.completed) && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mt-8">
            <div className="text-center">
              <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-3" />
              <h3 className="text-lg font-bold text-green-800 mb-2">🎉 测试完成！</h3>
              <p className="text-green-700 mb-4">
                所有测试步骤已成功完成，您的Token已添加到系统中
              </p>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => router.push('/profile')}
                  className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
                >
                  查看我的Token
                </button>
                <button
                  onClick={() => {
                    // 重置所有步骤
                    setSteps(prev => prev.map((step, index) => ({
                      ...step,
                      completed: false,
                      enabled: index === 0,
                      loading: false,
                      result: undefined,
                      error: undefined,
                    })));
                    setAuthUrl('');
                    setAuthResponse('');
                    setTestResult('');
                    setError('');
                    setSuccessMessage('');
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
                >
                  重新测试
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}