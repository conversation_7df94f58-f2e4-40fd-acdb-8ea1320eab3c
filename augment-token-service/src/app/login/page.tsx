/**
 * 用户登录页面
 * 提供邮箱登录功能，自动创建新用户
 */

'use client';

import { useState } from 'react';
import { Mail, Loader2, ArrowLeft, CheckCircle } from 'lucide-react';
import { useAuth, useAuthRedirect } from '@/hooks/useAuth';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [userToken, setUserToken] = useState('');

  const { login, isLoading } = useAuth();

  // 自动重定向逻辑
  useAuthRedirect();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess(false);

    try {
      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        setError('请输入有效的邮箱地址');
        return;
      }

      const result = await login(email);

      if (result.success) {
        setSuccess(true);
        // 获取用户Token显示给用户
        const token = localStorage.getItem('userToken');
        if (token) {
          setUserToken(token);
        }

        // 3秒后自动跳转（useAuth已经处理了跳转）
        setTimeout(() => {
          // 跳转逻辑已在useAuth中处理
        }, 3000);
      } else {
        setError(result.error || '登录失败，请重试');
      }
    } catch (err) {
      setError('网络请求失败，请检查网络连接');
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">登录成功！</h2>
            <p className="text-gray-600 mb-6">
              欢迎使用Augment Token服务
            </p>
            
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-700 mb-2">您的用户Token：</p>
              <code className="text-xs bg-gray-200 px-2 py-1 rounded break-all">
                {userToken}
              </code>
              <p className="text-xs text-gray-500 mt-2">
                请保存此Token，用于VSCode插件认证
              </p>
            </div>

            <p className="text-sm text-gray-500">
              正在跳转到管理面板...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          

          {/* 登录表单 */}
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">用户登录</h1>
            <p className="text-gray-600">
              输入邮箱地址登录或创建新账户
            </p>
          </div>

          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                邮箱地址
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="请输入您的邮箱地址"
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                  required
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading || !email}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  登录中...
                </>
              ) : (
                '登录 / 注册'
              )}
            </button>
          </form>

          {/* 说明文字 */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              首次使用将自动创建账户
            </p>
            <p className="text-xs text-gray-400 mt-2">
              登录即表示您同意我们的服务条款和隐私政策
            </p>
          </div>
        </div>

        {/* 功能说明 */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">服务特色</h3>
          <ul className="space-y-2 text-sm text-gray-600">
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
              智能Token池管理，自动负载均衡
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
              实时健康监控，确保服务稳定
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
              与VSCode插件完美兼容
            </li>
            <li className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
              企业级安全保障，数据加密存储
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
