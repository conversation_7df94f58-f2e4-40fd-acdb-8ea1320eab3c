/**
 * 主页 - 直接重定向到管理面板
 * 根据用户认证状态自动跳转
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

export default function HomePage() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        // 已登录用户直接跳转到管理面板
        router.replace('/dashboard');
      } else {
        // 未登录用户跳转到登录页面
        router.replace('/login');
      }
    }
  }, [isAuthenticated, isLoading, router]);

  // 直接返回null，让重定向立即生效
  return null;
}
