/**
 * 个人中心页面
 * 参考竞争对手设计，提供用户信息管理和Token管理功能
 */

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  User,
  Copy,
  Calendar,
  Clock,
  Shield,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  Settings,
  Key
} from 'lucide-react';
import { useAuth, useAuthRedirect } from '@/hooks/useAuth';
import Navigation, { PageHeader, Message } from '@/components/Navigation';

interface UserProfile {
  id: string;
  email: string;
  user_token: string;
  created_at: string;
  updated_at: string;
  subscription_type: string;
  is_active: boolean;
}

interface UserToken {
  id: string;
  token_value: string;
  tenant_url: string;
  created_at: string;
  last_used_at: string | null;
  usage_count: number;
  is_active: boolean;
  expires_at: string | null;
  allocated_to: string | null;  // 谁分配了这个Token
  allocated_at: string | null;  // 什么时候分配的
}

interface TokenListResponse {
  tokens: UserToken[];
  pagination: {
    page: number;
    limit: number;
    totalRecords: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export default function ProfilePage() {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [userTokens, setUserTokens] = useState<UserToken[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalRecords: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingTokens, setIsLoadingTokens] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const { user, logout, getUserToken, isAuthenticated } = useAuth();
  const router = useRouter();
  
  // 自动重定向逻辑
  useAuthRedirect();

  useEffect(() => {
    if (isAuthenticated) {
      loadProfileData();
      loadUserTokens(1, 10);
    }
  }, [isAuthenticated]);

  const loadProfileData = async () => {
    try {
      const userToken = getUserToken();
      if (!userToken) return;

      setIsLoading(true);

      // 获取用户详细信息
      const response = await fetch('/api/user/profile', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserProfile(data.data);
        } else {
          setError(data.error || '获取用户信息失败');
        }
      } else {
        setError('获取用户信息失败');
      }

    } catch (err) {
      setError('加载用户信息失败，请刷新页面重试');
    } finally {
      setIsLoading(false);
    }
  };

  const loadUserTokens = async (page: number, limit: number) => {
    try {
      const userToken = getUserToken();
      if (!userToken) {
        console.log('No user token available');
        return;
      }

      console.log('=== LOADING USER TOKENS ===');
      console.log('Page:', page, 'Limit:', limit);
      console.log('User token:', userToken);

      setIsLoadingTokens(true);

      const url = `/api/user/tokens?page=${page}&limit=${limit}`;
      console.log('Fetching URL:', url);

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (response.ok) {
        const data = await response.json();
        console.log('Response data:', data);

        if (data.success) {
          console.log('Tokens:', data.data.tokens);
          console.log('Pagination:', data.data.pagination);

          setUserTokens(data.data.tokens || []);
          setPagination(data.data.pagination);
        } else {
          console.error('API returned success: false', data.error);
          setError(data.error || '获取Token列表失败');
        }
      } else {
        console.error('HTTP error:', response.status);
        const errorText = await response.text();
        console.error('Error response:', errorText);

        // 如果API不存在，显示空列表
        setUserTokens([]);
        setPagination({
          page: 1,
          limit: 10,
          totalRecords: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        });
      }

    } catch (err) {
      console.error('Load tokens error:', err);
      setError('加载Token列表失败');
    } finally {
      setIsLoadingTokens(false);
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string, message: string = '已复制到剪贴板') => {
    navigator.clipboard.writeText(text);
    setSuccessMessage(message);
    setTimeout(() => setSuccessMessage(''), 2000);
  };

  // 处理分页变更
  const handlePageChange = (newPage: number) => {
    loadUserTokens(newPage, pagination.limit);
  };

  // 处理每页条数变更
  const handleLimitChange = (newLimit: number) => {
    loadUserTokens(1, newLimit);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证登录状态...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载用户信息...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* 导航栏 */}
        <Navigation className="mb-8" />

        {/* 页面标题 */}
        <PageHeader
          icon={<User className="w-8 h-8 text-blue-600" />}
          title="👤 个人中心"
          description="用户信息管理和Token管理"
          className="mb-8"
        />

        {/* 消息提示 */}
        {error && <Message type="error" message={error} className="mb-6" />}
        {successMessage && <Message type="success" message={successMessage} className="mb-6" />}

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 用户信息卡片 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center">
                <div className="w-20 h-20 bg-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                  {userProfile?.email?.charAt(0).toUpperCase() || 'T'}
                </div>
                <h1 className="text-xl font-bold text-gray-900 mb-1">
                  {userProfile?.email?.split('@')[0] || 'test'}
                </h1>
                <p className="text-gray-600 text-sm">{userProfile?.email}</p>
              </div>
            </div>
          </div>

          {/* 详细信息区域 */}
          <div className="lg:col-span-3 space-y-8">
            {/* 账户信息 */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                📊 账户信息
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600">用户ID</span>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm text-gray-800 bg-white px-2 py-1 rounded border">
                        {userProfile?.id || ''}
                      </code>
                      <button
                        onClick={() => copyToClipboard(userProfile?.id || '', '用户ID已复制')}
                        className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                        title="点击复制用户ID"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600">Token</span>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm text-gray-800 bg-white px-2 py-1 rounded border">
                        {userProfile?.user_token || ''}
                      </code>
                      <button
                        onClick={() => copyToClipboard(userProfile?.user_token || '', '用户Token已复制')}
                        className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                        title="点击复制用户Token"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600 flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      注册时间
                    </span>
                    <span className="text-gray-800 font-medium">
                      {userProfile?.created_at ? formatDate(userProfile.created_at) : ''}
                    </span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600 flex items-center">
                      <Clock className="w-4 h-4 mr-2" />
                      最后更新
                    </span>
                    <span className="text-gray-800 font-medium">
                      {userProfile?.updated_at ? formatDate(userProfile.updated_at) : ''}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* 我的Token */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <Key className="w-5 h-5 mr-2" />
                  🔑 我的Token
                </h3>
                <button
                  onClick={() => loadUserTokens(pagination.page, pagination.limit)}
                  className="flex items-center px-3 py-2 text-gray-600 hover:text-blue-600 transition-colors"
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  刷新
                </button>
              </div>

              {/* Token统计和分页控制 */}
              <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-gray-600">
                  总计: {pagination.totalRecords} 个Token
                </div>
                
                <select
                  value={pagination.limit}
                  onChange={(e) => handleLimitChange(parseInt(e.target.value))}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                >
                  <option value={5}>每页 5 条</option>
                  <option value={10}>每页 10 条</option>
                  <option value={20}>每页 20 条</option>
                  <option value={50}>每页 50 条</option>
                </select>
              </div>

              {/* Token列表 */}
              {isLoadingTokens ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
                  <p className="text-gray-600">正在加载Token列表...</p>
                </div>
              ) : userTokens.length > 0 ? (
                <div className="space-y-3">
                  {userTokens.map((token) => (
                    <div key={token.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Shield className={`w-4 h-4 ${token.is_active ? 'text-green-600' : 'text-red-600'}`} />
                            <span className="font-medium text-gray-900">
                              Token #{token.id.slice(-8)}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              token.is_active 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {token.is_active ? '活跃' : '已停用'}
                            </span>
                          </div>
                          
                          <div className="text-sm text-gray-600 space-y-2">
                            {/* 访问令牌 - 像竞争对手一样显示 */}
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <p className="font-medium text-gray-700">访问令牌：</p>
                                <button
                                  onClick={() => copyToClipboard(token.token_value, '访问令牌已复制')}
                                  className="text-blue-600 hover:text-blue-800 text-xs flex items-center"
                                >
                                  <Copy className="w-3 h-3 mr-1" />
                                  复制
                                </button>
                              </div>
                              <div className="bg-gray-50 p-2 rounded border font-mono text-xs break-all">
                                {token.token_value}
                              </div>
                            </div>

                            {/* 租户URL */}
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <p className="font-medium text-gray-700">租户URL：</p>
                                <button
                                  onClick={() => copyToClipboard(token.tenant_url, '租户URL已复制')}
                                  className="text-blue-600 hover:text-blue-800 text-xs flex items-center"
                                >
                                  <Copy className="w-3 h-3 mr-1" />
                                  复制
                                </button>
                              </div>
                              <div className="bg-gray-50 p-2 rounded border font-mono text-xs">
                                {token.tenant_url}
                              </div>
                            </div>

                            {/* 其他信息 */}
                            <div className="pt-2 border-t border-gray-100 space-y-1">
                              <p><span className="font-medium text-gray-700">使用次数:</span> {token.usage_count}</p>
                              <p><span className="font-medium text-gray-700">创建时间:</span> {formatDate(token.created_at)}</p>
                              {token.last_used_at && (
                                <p><span className="font-medium text-gray-700">最后使用:</span> {formatDate(token.last_used_at)}</p>
                              )}
                              {token.allocated_to && (
                                <p><span className="font-medium text-gray-700">分配给:</span> {token.allocated_to}</p>
                              )}
                              {token.allocated_at && (
                                <p><span className="font-medium text-gray-700">分配时间:</span> {formatDate(token.allocated_at)}</p>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <button
                          onClick={() => copyToClipboard(token.token_value, 'Token已复制')}
                          className="p-2 text-gray-500 hover:text-blue-600 transition-colors"
                          title="复制Token"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Key className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无Token</h3>
                  <p className="text-gray-500 mb-4">您还没有创建任何Token</p>
                  <button
                    onClick={() => router.push('/dashboard')}
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
                  >
                    去获取Token
                  </button>
                </div>
              )}

              {/* 分页控制 */}
              {pagination.totalPages > 1 && (
                <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={!pagination.hasPrev}
                    className="flex items-center px-3 py-2 text-gray-600 hover:text-blue-600 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
                  >
                    <ChevronLeft className="w-4 h-4 mr-1" />
                    上一页
                  </button>
                  
                  <span className="text-sm text-gray-600">
                    第 {pagination.page} 页，共 {pagination.totalPages} 页
                  </span>
                  
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={!pagination.hasNext}
                    className="flex items-center px-3 py-2 text-gray-600 hover:text-blue-600 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"
                  >
                    下一页
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
