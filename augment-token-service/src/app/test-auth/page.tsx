/**
 * 认证测试页面
 * 用于测试useAuth Hook的修复效果
 */

'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';

export default function TestAuthPage() {
  const { user, isLoading, isAuthenticated, getUserToken, refreshAuth } = useAuth();
  const [renderCount, setRenderCount] = useState(0);
  const [logs, setLogs] = useState<string[]>([]);
  const router = useRouter();

  // 计算渲染次数
  useEffect(() => {
    setRenderCount(prev => prev + 1);
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `${timestamp}: Component rendered (count: ${renderCount + 1})`]);
  });

  // 监控认证状态变化
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `${timestamp}: Auth state changed - authenticated: ${isAuthenticated}, loading: ${isLoading}`]);
  }, [isAuthenticated, isLoading]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `${timestamp}: ${message}`]);
  };

  const handleNavigateHome = () => {
    addLog('Navigating to dashboard...');
    router.push('/dashboard');
  };

  const handleRefreshAuth = () => {
    addLog('Manually refreshing auth...');
    refreshAuth();
  };

  const handleClearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">认证系统测试页面</h1>
          <p className="text-gray-600 mb-6">
            此页面用于测试useAuth Hook的修复效果，监控无限循环问题是否已解决。
          </p>

          {/* 认证状态显示 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">认证状态</h3>
              <p className={`text-sm ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                {isAuthenticated ? '已认证' : '未认证'}
              </p>
            </div>

            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="font-semibold text-yellow-900 mb-2">加载状态</h3>
              <p className={`text-sm ${isLoading ? 'text-orange-600' : 'text-green-600'}`}>
                {isLoading ? '加载中...' : '已完成'}
              </p>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-semibold text-purple-900 mb-2">渲染次数</h3>
              <p className={`text-sm font-mono ${renderCount > 10 ? 'text-red-600' : 'text-green-600'}`}>
                {renderCount} 次
                {renderCount > 10 && ' (可能存在无限循环!)'}
              </p>
            </div>
          </div>

          {/* 用户信息 */}
          {user && (
            <div className="bg-green-50 p-4 rounded-lg mb-6">
              <h3 className="font-semibold text-green-900 mb-2">用户信息</h3>
              <div className="text-sm text-green-700">
                <p><strong>邮箱:</strong> {user.email}</p>
                <p><strong>订阅类型:</strong> {user.subscription_type}</p>
                <p><strong>Token:</strong> {getUserToken()?.substring(0, 20)}...</p>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex flex-wrap gap-4 mb-6">
            <button
              onClick={handleNavigateHome}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              导航到主页
            </button>

            <button
              onClick={handleRefreshAuth}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              刷新认证状态
            </button>

            <button
              onClick={handleClearLogs}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              清除日志
            </button>

            <button
              onClick={() => router.push('/profile')}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              个人中心
            </button>
          </div>
        </div>

        {/* 日志显示 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">实时日志</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-500">暂无日志...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
          
          {/* 性能指标 */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">性能指标</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600">渲染次数:</span>
                <span className={`ml-2 font-semibold ${renderCount > 10 ? 'text-red-600' : 'text-green-600'}`}>
                  {renderCount}
                </span>
              </div>
              <div>
                <span className="text-gray-600">日志条数:</span>
                <span className="ml-2 font-semibold text-blue-600">{logs.length}</span>
              </div>
              <div>
                <span className="text-gray-600">认证状态:</span>
                <span className={`ml-2 font-semibold ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                  {isAuthenticated ? '正常' : '异常'}
                </span>
              </div>
              <div>
                <span className="text-gray-600">加载状态:</span>
                <span className={`ml-2 font-semibold ${isLoading ? 'text-orange-600' : 'text-green-600'}`}>
                  {isLoading ? '加载中' : '完成'}
                </span>
              </div>
            </div>
          </div>

          {/* 诊断信息 */}
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">诊断信息</h3>
            <div className="text-sm text-blue-700">
              {renderCount <= 5 && (
                <p className="text-green-600">✅ 渲染次数正常，无无限循环问题</p>
              )}
              {renderCount > 5 && renderCount <= 10 && (
                <p className="text-yellow-600">⚠️ 渲染次数较多，需要关注</p>
              )}
              {renderCount > 10 && (
                <p className="text-red-600">❌ 渲染次数过多，可能存在无限循环</p>
              )}
              
              {!isLoading && isAuthenticated && (
                <p className="text-green-600 mt-1">✅ 认证状态稳定</p>
              )}
              
              {isLoading && (
                <p className="text-yellow-600 mt-1">⏳ 正在加载认证状态...</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
