/**
 * 欢迎页面 - OAuth授权流程页面
 * 提供用户友好的Augment Token获取界面
 * (原主页内容，作为备用页面保存)
 */

'use client';

import { useState } from 'react';
import { Shield, Zap, Users, CheckCircle, Loader2 } from 'lucide-react';
import { useAuthRedirect } from '@/hooks/useAuth';

export default function WelcomePage() {
  const [isLoading, setIsLoading] = useState(false);
  const [authUrl, setAuthUrl] = useState<string>('');
  const [error, setError] = useState<string>('');

  // 自动重定向逻辑
  useAuthRedirect();

  const handleGetAuthUrl = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok && data.authorize_url) {
        setAuthUrl(data.authorize_url);
        // 自动打开授权页面
        window.open(data.authorize_url, '_blank');
      } else {
        setError(data.error || '获取授权链接失败');
      }
    } catch (err) {
      setError('网络请求失败，请检查网络连接');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* 头部标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Augment Token 无感换号服务
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            智能Token池管理，让您的Augment使用体验更加流畅无忧
          </p>
        </div>

        {/* 功能特色 */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <Shield className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">安全可靠</h3>
            <p className="text-gray-600">
              企业级安全保障，Token加密存储，多重身份验证
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <Zap className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">智能分配</h3>
            <p className="text-gray-600">
              AI驱动的Token分配算法，自动负载均衡，最优使用体验
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <Users className="w-12 h-12 text-purple-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">无感切换</h3>
            <p className="text-gray-600">
              一键更新Token，无需手动操作，真正的无感使用体验
            </p>
          </div>
        </div>

        {/* 主要操作区域 */}
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold text-center mb-2">开始使用</h2>
            <p className="text-gray-600 text-center mb-8">
              点击下方按钮获取Augment授权，将Token添加到我们的智能池中
            </p>

            {/* 使用步骤 */}
            <div className="space-y-4 mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  1
                </div>
                <span>点击"获取授权链接"按钮</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  2
                </div>
                <span>在弹出页面中完成Augment授权</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  3
                </div>
                <span>授权完成后，Token将自动添加到池中</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  ✓
                </div>
                <span>前往用户中心管理您的Token</span>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="text-center space-y-4">
              <button
                onClick={handleGetAuthUrl}
                disabled={isLoading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-6 rounded-lg transition-colors flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    获取授权链接中...
                  </>
                ) : (
                  <>
                    <Shield className="w-4 h-4 mr-2" />
                    获取授权链接
                  </>
                )}
              </button>

              <div className="flex space-x-4">
                <a
                  href="/login"
                  className="flex-1 border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors text-center"
                >
                  用户登录
                </a>
                <a
                  href="/dashboard"
                  className="flex-1 border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors text-center"
                >
                  管理面板
                </a>
              </div>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {/* 授权链接显示 */}
            {authUrl && (
              <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  <p className="text-green-700">
                    授权链接已生成并在新窗口中打开。如果没有自动打开，请点击
                    <a
                      href={authUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline ml-1"
                    >
                      这里
                    </a>
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* 服务说明 */}
          <div className="mt-8 text-center text-sm text-gray-500">
            <p>本服务仅供学习和研究使用，请遵守相关法律法规</p>
            <p className="mt-2">
              技术支持：
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
