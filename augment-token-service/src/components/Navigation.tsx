/**
 * 统一导航栏组件
 * 提供一致的导航体验和用户信息显示
 */

'use client';

import { useRouter, usePathname } from 'next/navigation';
import {
  Home,
  User,
  BookOpen,
  TestTube,
  LogOut,
  Settings,
  Activity
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

interface NavigationProps {
  className?: string;
}

export default function Navigation({ className = '' }: NavigationProps) {
  const { user, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const navigationItems = [
    {
      name: '主页',
      path: '/dashboard',
      icon: Home,
      description: 'OAuth授权管理'
    },
    {
      name: '个人中心',
      path: '/profile',
      icon: User,
      description: '用户信息和Token管理'
    },
    {
      name: 'API文档',
      path: '/api-docs',
      icon: BookOpen,
      description: '外部API接口文档'
    },
    {
      name: '外部接口测试',
      path: '/external-test',
      icon: TestTube,
      description: 'API测试工具'
    }
  ];

  const isActivePath = (path: string) => {
    if (path === '/dashboard') {
      return pathname === '/' || pathname === '/dashboard';
    }
    return pathname === path;
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-6">
          <button
            onClick={() => router.push('/dashboard')}
            className="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors"
          >
             Augment无感切号
          </button>
          
          <div className="flex items-center space-x-1">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
              {user?.email?.charAt(0).toUpperCase() || 'T'}
            </div>
            <span className="text-gray-700">欢迎，{user?.email}</span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = isActivePath(item.path);
            
            return (
              <button
                key={item.path}
                onClick={() => router.push(item.path)}
                className={`flex items-center px-3 py-2 rounded-lg transition-colors ${
                  isActive
                    ? 'bg-blue-100 text-blue-700 font-medium'
                    : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                }`}
                title={item.description}
              >
                <Icon className="w-4 h-4 mr-1" />
                <span className="hidden sm:inline">{item.name}</span>
              </button>
            );
          })}
          
          <div className="w-px h-6 bg-gray-300 mx-2"></div>
          
          <button
            onClick={logout}
            className="flex items-center px-3 py-2 text-gray-600 hover:text-red-600 transition-colors rounded-lg hover:bg-red-50"
            title="退出登录"
          >
            <LogOut className="w-4 h-4 mr-1" />
            <span className="hidden sm:inline">登出</span>
          </button>
        </div>
      </div>
    </div>
  );
}

/**
 * 页面标题组件
 */
interface PageHeaderProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  className?: string;
}

export function PageHeader({ icon, title, description, className = '' }: PageHeaderProps) {
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center">
        {icon}
        <div className="ml-3">
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          <p className="text-gray-600 mt-1">{description}</p>
        </div>
      </div>
    </div>
  );
}

/**
 * 消息提示组件
 */
interface MessageProps {
  type: 'success' | 'error' | 'info' | 'warning';
  message: string;
  className?: string;
}

export function Message({ type, message, className = '' }: MessageProps) {
  const styles = {
    success: {
      container: 'bg-green-50 border border-green-200',
      icon: 'text-green-600',
      text: 'text-green-700',
      IconComponent: Settings
    },
    error: {
      container: 'bg-red-50 border border-red-200',
      icon: 'text-red-600',
      text: 'text-red-700',
      IconComponent: Activity
    },
    info: {
      container: 'bg-blue-50 border border-blue-200',
      icon: 'text-blue-600',
      text: 'text-blue-700',
      IconComponent: Settings
    },
    warning: {
      container: 'bg-yellow-50 border border-yellow-200',
      icon: 'text-yellow-600',
      text: 'text-yellow-700',
      IconComponent: Activity
    }
  };

  const style = styles[type];
  const IconComponent = style.IconComponent;

  return (
    <div className={`${style.container} rounded-lg p-4 ${className}`}>
      <div className="flex items-center">
        <IconComponent className={`w-5 h-5 ${style.icon} mr-2`} />
        <p className={style.text}>{message}</p>
      </div>
    </div>
  );
}
