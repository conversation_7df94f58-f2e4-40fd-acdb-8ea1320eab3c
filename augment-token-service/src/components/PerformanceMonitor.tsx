/**
 * 性能监控组件
 * 实时监控前端性能指标
 */

import React, { useState, useEffect, useRef } from 'react';
import { Activity, Zap, Database, Clock, TrendingUp, AlertTriangle } from 'lucide-react';

interface PerformanceMetrics {
  responseTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  renderTime: number;
  apiCalls: number;
  errors: number;
  timestamp: number;
}

interface PerformanceMonitorProps {
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  showDetails?: boolean;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  className = '',
  autoRefresh = true,
  refreshInterval = 5000,
  showDetails = false,
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [history, setHistory] = useState<PerformanceMetrics[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  // 收集性能指标
  const collectMetrics = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const startTime = performance.now();

      // 获取内存使用情况
      const memoryUsage = getMemoryUsage();

      // 测试API响应时间
      const apiStartTime = performance.now();
      const response = await fetch('/api/admin/performance-test', {
        method: 'GET',
        headers: {
          'X-API-Key': 'our_api_key_v1_2024',
        },
      });
      const apiEndTime = performance.now();
      const responseTime = apiEndTime - apiStartTime;

      const data = await response.json();
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      const newMetrics: PerformanceMetrics = {
        responseTime,
        cacheHitRate: data.data?.cache?.hitRate || 0,
        memoryUsage,
        renderTime,
        apiCalls: 1,
        errors: response.ok ? 0 : 1,
        timestamp: Date.now(),
      };

      setMetrics(newMetrics);
      setHistory(prev => [...prev.slice(-19), newMetrics]); // 保留最近20条记录

    } catch (err) {
      setError(err instanceof Error ? err.message : '性能监控失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 获取内存使用情况
  const getMemoryUsage = (): number => {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in window.performance) {
      return (window.performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  };

  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      collectMetrics(); // 立即执行一次
      
      intervalRef.current = setInterval(collectMetrics, refreshInterval);
      
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval]);

  // 计算性能状态
  const getPerformanceStatus = (metrics: PerformanceMetrics) => {
    if (metrics.responseTime > 1000 || metrics.errors > 0) {
      return { status: 'poor', color: 'text-red-600', bgColor: 'bg-red-100' };
    }
    if (metrics.responseTime > 500) {
      return { status: 'fair', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    }
    return { status: 'good', color: 'text-green-600', bgColor: 'bg-green-100' };
  };

  // 格式化数值
  const formatValue = (value: number, unit: string, decimals: number = 1): string => {
    return `${value.toFixed(decimals)}${unit}`;
  };

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5 text-red-600" />
          <span className="text-red-700 font-medium">性能监控错误</span>
        </div>
        <p className="text-red-600 text-sm mt-1">{error}</p>
        <button
          onClick={collectMetrics}
          className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
        >
          重试
        </button>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-gray-600">正在收集性能数据...</span>
        </div>
      </div>
    );
  }

  const performanceStatus = getPerformanceStatus(metrics);

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900">性能监控</h3>
          <div className={`px-2 py-1 rounded-full text-xs font-medium ${performanceStatus.bgColor} ${performanceStatus.color}`}>
            {performanceStatus.status === 'good' && '优秀'}
            {performanceStatus.status === 'fair' && '良好'}
            {performanceStatus.status === 'poor' && '需要优化'}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {isLoading && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          )}
          <button
            onClick={collectMetrics}
            disabled={isLoading}
            className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm rounded transition-colors disabled:opacity-50"
          >
            刷新
          </button>
        </div>
      </div>

      {/* 性能指标 */}
      <div className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {/* 响应时间 */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-4 h-4 text-blue-600 mr-1" />
              <span className="text-sm text-gray-600">响应时间</span>
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {formatValue(metrics.responseTime, 'ms', 0)}
            </div>
          </div>

          {/* 缓存命中率 */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Zap className="w-4 h-4 text-green-600 mr-1" />
              <span className="text-sm text-gray-600">缓存命中</span>
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {formatValue(metrics.cacheHitRate * 100, '%', 0)}
            </div>
          </div>

          {/* 内存使用 */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Database className="w-4 h-4 text-purple-600 mr-1" />
              <span className="text-sm text-gray-600">内存使用</span>
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {formatValue(metrics.memoryUsage, 'MB')}
            </div>
          </div>

          {/* 渲染时间 */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="w-4 h-4 text-orange-600 mr-1" />
              <span className="text-sm text-gray-600">渲染时间</span>
            </div>
            <div className="text-lg font-semibold text-gray-900">
              {formatValue(metrics.renderTime, 'ms', 0)}
            </div>
          </div>
        </div>

        {/* 详细信息 */}
        {showDetails && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-2">详细信息</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">API调用次数:</span>
                <span className="ml-2 font-medium">{metrics.apiCalls}</span>
              </div>
              <div>
                <span className="text-gray-600">错误次数:</span>
                <span className="ml-2 font-medium">{metrics.errors}</span>
              </div>
              <div>
                <span className="text-gray-600">最后更新:</span>
                <span className="ml-2 font-medium">
                  {new Date(metrics.timestamp).toLocaleTimeString()}
                </span>
              </div>
              <div>
                <span className="text-gray-600">历史记录:</span>
                <span className="ml-2 font-medium">{history.length} 条</span>
              </div>
            </div>
          </div>
        )}

        {/* 性能趋势 */}
        {history.length > 1 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-2">性能趋势</h4>
            <div className="h-16 flex items-end space-x-1">
              {history.slice(-10).map((metric, index) => {
                const height = Math.max(4, (metric.responseTime / 1000) * 60);
                const color = metric.responseTime > 500 ? 'bg-red-400' : 
                             metric.responseTime > 200 ? 'bg-yellow-400' : 'bg-green-400';
                
                return (
                  <div
                    key={index}
                    className={`w-3 ${color} rounded-t`}
                    style={{ height: `${Math.min(height, 60)}px` }}
                    title={`${metric.responseTime.toFixed(0)}ms`}
                  />
                );
              })}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              最近10次响应时间趋势
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
