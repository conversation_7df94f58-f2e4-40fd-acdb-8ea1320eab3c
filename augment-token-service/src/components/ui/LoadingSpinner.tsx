/**
 * 高性能Loading组件库
 * 提供多种loading状态和动画效果
 */

import React from 'react';
import { cn } from '@/lib/utils';

// Loading Spinner 变体类型
export type SpinnerVariant = 'default' | 'dots' | 'pulse' | 'bounce' | 'wave' | 'ring' | 'gradient';

// Loading Spinner 尺寸类型
export type SpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Loading Spinner 属性接口
interface LoadingSpinnerProps {
  variant?: SpinnerVariant;
  size?: SpinnerSize;
  color?: string;
  className?: string;
  text?: string;
  textPosition?: 'bottom' | 'right' | 'top' | 'left';
  fullScreen?: boolean;
  overlay?: boolean;
  children?: React.ReactNode;
}

// 尺寸映射
const sizeClasses = {
  xs: 'w-4 h-4',
  sm: 'w-6 h-6',
  md: 'w-8 h-8',
  lg: 'w-12 h-12',
  xl: 'w-16 h-16',
};

// 文本尺寸映射
const textSizeClasses = {
  xs: 'text-xs',
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl',
};

// 默认Spinner组件
const DefaultSpinner: React.FC<{ size: SpinnerSize; color: string; className?: string }> = ({ 
  size, 
  color, 
  className 
}) => (
  <div
    className={cn(
      'animate-spin rounded-full border-2 border-transparent',
      sizeClasses[size],
      className
    )}
    style={{
      borderTopColor: color,
      borderRightColor: color,
    }}
  />
);

// 点状Loading组件
const DotsSpinner: React.FC<{ size: SpinnerSize; color: string; className?: string }> = ({ 
  size, 
  color, 
  className 
}) => {
  const dotSize = size === 'xs' ? 'w-1 h-1' : size === 'sm' ? 'w-1.5 h-1.5' : 
                 size === 'md' ? 'w-2 h-2' : size === 'lg' ? 'w-3 h-3' : 'w-4 h-4';
  
  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn('rounded-full animate-pulse', dotSize)}
          style={{ 
            backgroundColor: color,
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1.4s'
          }}
        />
      ))}
    </div>
  );
};

// 脉冲Loading组件
const PulseSpinner: React.FC<{ size: SpinnerSize; color: string; className?: string }> = ({ 
  size, 
  color, 
  className 
}) => (
  <div
    className={cn(
      'animate-pulse rounded-full',
      sizeClasses[size],
      className
    )}
    style={{ backgroundColor: color }}
  />
);

// 弹跳Loading组件
const BounceSpinner: React.FC<{ size: SpinnerSize; color: string; className?: string }> = ({ 
  size, 
  color, 
  className 
}) => {
  const ballSize = size === 'xs' ? 'w-2 h-2' : size === 'sm' ? 'w-3 h-3' : 
                  size === 'md' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-8 h-8';
  
  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn('rounded-full animate-bounce', ballSize)}
          style={{ 
            backgroundColor: color,
            animationDelay: `${i * 0.1}s`
          }}
        />
      ))}
    </div>
  );
};

// 波浪Loading组件
const WaveSpinner: React.FC<{ size: SpinnerSize; color: string; className?: string }> = ({ 
  size, 
  color, 
  className 
}) => {
  const barHeight = size === 'xs' ? 'h-4' : size === 'sm' ? 'h-6' : 
                   size === 'md' ? 'h-8' : size === 'lg' ? 'h-12' : 'h-16';
  
  return (
    <div className={cn('flex items-end space-x-1', className)}>
      {[0, 1, 2, 3, 4].map((i) => (
        <div
          key={i}
          className={cn('w-1 animate-pulse', barHeight)}
          style={{ 
            backgroundColor: color,
            animationDelay: `${i * 0.1}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );
};

// 环形Loading组件
const RingSpinner: React.FC<{ size: SpinnerSize; color: string; className?: string }> = ({ 
  size, 
  color, 
  className 
}) => (
  <div
    className={cn(
      'animate-spin rounded-full border-4 border-gray-200',
      sizeClasses[size],
      className
    )}
    style={{ borderTopColor: color }}
  />
);

// 渐变Loading组件
const GradientSpinner: React.FC<{ size: SpinnerSize; color: string; className?: string }> = ({ 
  size, 
  color, 
  className 
}) => (
  <div
    className={cn(
      'animate-spin rounded-full',
      sizeClasses[size],
      className
    )}
    style={{
      background: `conic-gradient(from 0deg, transparent, ${color})`,
    }}
  />
);

// 主Loading组件
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  variant = 'default',
  size = 'md',
  color = '#3B82F6',
  className,
  text,
  textPosition = 'bottom',
  fullScreen = false,
  overlay = false,
  children,
}) => {
  // 选择Spinner组件
  const SpinnerComponent = {
    default: DefaultSpinner,
    dots: DotsSpinner,
    pulse: PulseSpinner,
    bounce: BounceSpinner,
    wave: WaveSpinner,
    ring: RingSpinner,
    gradient: GradientSpinner,
  }[variant];

  // 渲染Spinner内容
  const spinnerContent = (
    <div className={cn(
      'flex items-center justify-center',
      textPosition === 'right' && 'flex-row space-x-3',
      textPosition === 'left' && 'flex-row-reverse space-x-reverse space-x-3',
      textPosition === 'top' && 'flex-col-reverse space-y-reverse space-y-3',
      textPosition === 'bottom' && 'flex-col space-y-3',
    )}>
      <SpinnerComponent size={size} color={color} className={className} />
      {text && (
        <span className={cn(
          'text-gray-600 font-medium',
          textSizeClasses[size]
        )}>
          {text}
        </span>
      )}
    </div>
  );

  // 全屏模式
  if (fullScreen) {
    return (
      <div className={cn(
        'fixed inset-0 z-50 flex items-center justify-center',
        overlay && 'bg-black bg-opacity-50'
      )}>
        <div className="bg-white rounded-lg p-8 shadow-lg">
          {spinnerContent}
        </div>
      </div>
    );
  }

  // 普通模式
  return (
    <div className="flex items-center justify-center p-4">
      {spinnerContent}
      {children}
    </div>
  );
};

// 快捷Loading组件
export const QuickLoader: React.FC<{ text?: string }> = ({ text = '加载中...' }) => (
  <LoadingSpinner variant="default" size="md" text={text} />
);

// 页面Loading组件
export const PageLoader: React.FC<{ text?: string }> = ({ text = '正在加载页面...' }) => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
    <div className="text-center">
      <LoadingSpinner variant="gradient" size="lg" color="#3B82F6" />
      <p className="text-gray-600 mt-4 text-lg">{text}</p>
    </div>
  </div>
);

// 按钮Loading组件
export const ButtonLoader: React.FC<{ text?: string }> = ({ text = '处理中...' }) => (
  <div className="flex items-center space-x-2">
    <LoadingSpinner variant="default" size="sm" color="#FFFFFF" />
    <span>{text}</span>
  </div>
);

export default LoadingSpinner;
