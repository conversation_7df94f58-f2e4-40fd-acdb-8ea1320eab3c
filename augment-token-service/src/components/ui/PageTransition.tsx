/**
 * 页面过渡动画组件
 * 提供平滑的页面切换效果
 */

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

// 过渡动画类型
export type TransitionType = 'fade' | 'slide' | 'scale' | 'flip' | 'blur' | 'none';

// 过渡方向类型
export type TransitionDirection = 'up' | 'down' | 'left' | 'right';

// 过渡动画属性接口
interface PageTransitionProps {
  children: React.ReactNode;
  type?: TransitionType;
  direction?: TransitionDirection;
  duration?: number;
  delay?: number;
  className?: string;
  isVisible?: boolean;
  onTransitionEnd?: () => void;
}

// 过渡状态类型
type TransitionState = 'entering' | 'entered' | 'exiting' | 'exited';

// 获取过渡类名
const getTransitionClasses = (
  type: TransitionType,
  direction: TransitionDirection,
  state: TransitionState,
  duration: number
): string => {
  const baseClasses = `transition-all duration-${duration}`;
  
  switch (type) {
    case 'fade':
      return cn(
        baseClasses,
        state === 'entering' || state === 'entered' ? 'opacity-100' : 'opacity-0'
      );
      
    case 'slide':
      const slideClasses = {
        up: state === 'entering' || state === 'entered' ? 'translate-y-0' : 'translate-y-full',
        down: state === 'entering' || state === 'entered' ? 'translate-y-0' : '-translate-y-full',
        left: state === 'entering' || state === 'entered' ? 'translate-x-0' : 'translate-x-full',
        right: state === 'entering' || state === 'entered' ? 'translate-x-0' : '-translate-x-full',
      };
      return cn(baseClasses, slideClasses[direction]);
      
    case 'scale':
      return cn(
        baseClasses,
        state === 'entering' || state === 'entered' ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
      );
      
    case 'flip':
      return cn(
        baseClasses,
        'transform-gpu',
        state === 'entering' || state === 'entered' ? 'rotateY-0 opacity-100' : 'rotateY-90 opacity-0'
      );
      
    case 'blur':
      return cn(
        baseClasses,
        state === 'entering' || state === 'entered' ? 'blur-0 opacity-100' : 'blur-sm opacity-0'
      );
      
    default:
      return '';
  }
};

// 页面过渡组件
export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  type = 'fade',
  direction = 'up',
  duration = 300,
  delay = 0,
  className,
  isVisible = true,
  onTransitionEnd,
}) => {
  const [state, setState] = useState<TransitionState>(isVisible ? 'entered' : 'exited');
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (isVisible) {
      setState('entering');
      timeoutRef.current = setTimeout(() => {
        setState('entered');
        onTransitionEnd?.();
      }, delay + duration);
    } else {
      setState('exiting');
      timeoutRef.current = setTimeout(() => {
        setState('exited');
        onTransitionEnd?.();
      }, duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isVisible, duration, delay, onTransitionEnd]);

  if (state === 'exited') {
    return null;
  }

  return (
    <div
      className={cn(
        getTransitionClasses(type, direction, state, duration),
        className
      )}
      style={{ transitionDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
};

// 路由过渡包装器
export const RouteTransition: React.FC<{
  children: React.ReactNode;
  pathname: string;
  className?: string;
}> = ({ children, pathname, className }) => {
  const [currentPath, setCurrentPath] = useState(pathname);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (pathname !== currentPath) {
      setIsTransitioning(true);
      
      // 延迟更新路径以实现过渡效果
      const timer = setTimeout(() => {
        setCurrentPath(pathname);
        setIsTransitioning(false);
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [pathname, currentPath]);

  return (
    <div className={cn('relative overflow-hidden', className)}>
      <PageTransition
        type="fade"
        isVisible={!isTransitioning}
        duration={150}
      >
        {children}
      </PageTransition>
    </div>
  );
};

// 加载状态过渡组件
export const LoadingTransition: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
  className?: string;
}> = ({ isLoading, children, loadingComponent, className }) => {
  return (
    <div className={cn('relative', className)}>
      <PageTransition
        type="fade"
        isVisible={!isLoading}
        duration={200}
      >
        {children}
      </PageTransition>
      
      <PageTransition
        type="fade"
        isVisible={isLoading}
        duration={200}
        className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90"
      >
        {loadingComponent || (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">加载中...</span>
          </div>
        )}
      </PageTransition>
    </div>
  );
};

// 错误状态过渡组件
export const ErrorTransition: React.FC<{
  hasError: boolean;
  children: React.ReactNode;
  errorComponent?: React.ReactNode;
  className?: string;
}> = ({ hasError, children, errorComponent, className }) => {
  return (
    <div className={cn('relative', className)}>
      <PageTransition
        type="fade"
        isVisible={!hasError}
        duration={200}
      >
        {children}
      </PageTransition>
      
      <PageTransition
        type="scale"
        isVisible={hasError}
        duration={300}
        className="absolute inset-0 flex items-center justify-center bg-red-50"
      >
        {errorComponent || (
          <div className="text-center p-8">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-red-700 mb-2">出现错误</h3>
            <p className="text-red-600">请刷新页面重试</p>
          </div>
        )}
      </PageTransition>
    </div>
  );
};

// 列表项过渡组件
export const ListItemTransition: React.FC<{
  children: React.ReactNode;
  index: number;
  className?: string;
}> = ({ children, index, className }) => {
  return (
    <PageTransition
      type="slide"
      direction="up"
      duration={200}
      delay={index * 50}
      className={className}
    >
      {children}
    </PageTransition>
  );
};

// 模态框过渡组件
export const ModalTransition: React.FC<{
  isOpen: boolean;
  children: React.ReactNode;
  onClose?: () => void;
  className?: string;
}> = ({ isOpen, children, onClose, className }) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <PageTransition
        type="fade"
        isVisible={isOpen}
        duration={200}
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* 模态框内容 */}
      <PageTransition
        type="scale"
        isVisible={isOpen}
        duration={300}
        className={cn('relative z-10', className)}
      >
        {children}
      </PageTransition>
    </div>
  );
};

// 通知过渡组件
export const NotificationTransition: React.FC<{
  isVisible: boolean;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  className?: string;
}> = ({ isVisible, children, position = 'top-right', className }) => {
  const positionClasses = {
    'top': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom': 'bottom-4 left-1/2 transform -translate-x-1/2',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
  };

  const slideDirection = position.includes('top') ? 'down' : 'up';

  return (
    <PageTransition
      type="slide"
      direction={slideDirection}
      isVisible={isVisible}
      duration={300}
      className={cn(
        'fixed z-50',
        positionClasses[position],
        className
      )}
    >
      {children}
    </PageTransition>
  );
};

export default PageTransition;
