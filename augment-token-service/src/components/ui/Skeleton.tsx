/**
 * 高质量骨架屏组件库
 * 提供多种骨架屏布局和动画效果
 */

import React from 'react';
import { cn } from '@/lib/utils';

// 骨架屏动画类型
export type SkeletonAnimation = 'pulse' | 'wave' | 'shimmer' | 'none';

// 骨架屏变体类型
export type SkeletonVariant = 'rectangular' | 'circular' | 'rounded' | 'text';

// 基础骨架屏属性接口
interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  animation?: SkeletonAnimation;
  variant?: SkeletonVariant;
  children?: React.ReactNode;
}

// 动画类名映射
const animationClasses = {
  pulse: 'animate-pulse',
  wave: 'animate-wave',
  shimmer: 'animate-shimmer',
  none: '',
};

// 变体类名映射
const variantClasses = {
  rectangular: 'rounded-none',
  circular: 'rounded-full',
  rounded: 'rounded-md',
  text: 'rounded-sm',
};

// 基础骨架屏组件
export const Skeleton: React.FC<SkeletonProps> = ({
  className,
  width,
  height,
  animation = 'pulse',
  variant = 'rectangular',
  children,
}) => {
  const style: React.CSSProperties = {};
  
  if (width) {
    style.width = typeof width === 'number' ? `${width}px` : width;
  }
  
  if (height) {
    style.height = typeof height === 'number' ? `${height}px` : height;
  }

  return (
    <div
      className={cn(
        'bg-gray-200 dark:bg-gray-700',
        animationClasses[animation],
        variantClasses[variant],
        className
      )}
      style={style}
    >
      {children}
    </div>
  );
};

// 文本骨架屏组件
export const TextSkeleton: React.FC<{
  lines?: number;
  className?: string;
  animation?: SkeletonAnimation;
}> = ({ lines = 1, className, animation = 'pulse' }) => (
  <div className={cn('space-y-2', className)}>
    {Array.from({ length: lines }).map((_, index) => (
      <Skeleton
        key={index}
        variant="text"
        height={16}
        width={index === lines - 1 ? '75%' : '100%'}
        animation={animation}
      />
    ))}
  </div>
);

// 头像骨架屏组件
export const AvatarSkeleton: React.FC<{
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  animation?: SkeletonAnimation;
}> = ({ size = 'md', className, animation = 'pulse' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
  };

  return (
    <Skeleton
      variant="circular"
      className={cn(sizeClasses[size], className)}
      animation={animation}
    />
  );
};

// 卡片骨架屏组件
export const CardSkeleton: React.FC<{
  className?: string;
  animation?: SkeletonAnimation;
  showAvatar?: boolean;
  showImage?: boolean;
  textLines?: number;
}> = ({ 
  className, 
  animation = 'pulse', 
  showAvatar = false, 
  showImage = false,
  textLines = 3 
}) => (
  <div className={cn('p-4 space-y-4', className)}>
    {/* 头部区域 */}
    <div className="flex items-center space-x-3">
      {showAvatar && <AvatarSkeleton size="md" animation={animation} />}
      <div className="flex-1 space-y-2">
        <Skeleton variant="text" height={16} width="60%" animation={animation} />
        <Skeleton variant="text" height={12} width="40%" animation={animation} />
      </div>
    </div>

    {/* 图片区域 */}
    {showImage && (
      <Skeleton variant="rounded" height={200} width="100%" animation={animation} />
    )}

    {/* 文本区域 */}
    <TextSkeleton lines={textLines} animation={animation} />

    {/* 操作按钮区域 */}
    <div className="flex space-x-2">
      <Skeleton variant="rounded" height={32} width={80} animation={animation} />
      <Skeleton variant="rounded" height={32} width={80} animation={animation} />
    </div>
  </div>
);

// 表格骨架屏组件
export const TableSkeleton: React.FC<{
  rows?: number;
  columns?: number;
  className?: string;
  animation?: SkeletonAnimation;
}> = ({ rows = 5, columns = 4, className, animation = 'pulse' }) => (
  <div className={cn('space-y-3', className)}>
    {/* 表头 */}
    <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
      {Array.from({ length: columns }).map((_, index) => (
        <Skeleton
          key={`header-${index}`}
          variant="text"
          height={20}
          width="80%"
          animation={animation}
        />
      ))}
    </div>

    {/* 表格行 */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div
        key={`row-${rowIndex}`}
        className="grid gap-4"
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton
            key={`cell-${rowIndex}-${colIndex}`}
            variant="text"
            height={16}
            width={colIndex === 0 ? '90%' : '70%'}
            animation={animation}
          />
        ))}
      </div>
    ))}
  </div>
);

// 列表骨架屏组件
export const ListSkeleton: React.FC<{
  items?: number;
  className?: string;
  animation?: SkeletonAnimation;
  showAvatar?: boolean;
}> = ({ items = 5, className, animation = 'pulse', showAvatar = true }) => (
  <div className={cn('space-y-4', className)}>
    {Array.from({ length: items }).map((_, index) => (
      <div key={index} className="flex items-center space-x-3">
        {showAvatar && <AvatarSkeleton size="sm" animation={animation} />}
        <div className="flex-1 space-y-2">
          <Skeleton variant="text" height={16} width="70%" animation={animation} />
          <Skeleton variant="text" height={12} width="50%" animation={animation} />
        </div>
        <Skeleton variant="rounded" height={24} width={60} animation={animation} />
      </div>
    ))}
  </div>
);

// 统计卡片骨架屏组件
export const StatCardSkeleton: React.FC<{
  className?: string;
  animation?: SkeletonAnimation;
}> = ({ className, animation = 'pulse' }) => (
  <div className={cn('p-6 space-y-4', className)}>
    {/* 图标和标题 */}
    <div className="flex items-center justify-between">
      <div className="space-y-2">
        <Skeleton variant="text" height={14} width={100} animation={animation} />
        <Skeleton variant="text" height={24} width={80} animation={animation} />
      </div>
      <Skeleton variant="circular" width={40} height={40} animation={animation} />
    </div>

    {/* 数值 */}
    <Skeleton variant="text" height={32} width={120} animation={animation} />

    {/* 趋势指示器 */}
    <div className="flex items-center space-x-2">
      <Skeleton variant="rounded" height={16} width={16} animation={animation} />
      <Skeleton variant="text" height={12} width={80} animation={animation} />
    </div>
  </div>
);

// 导航栏骨架屏组件
export const NavigationSkeleton: React.FC<{
  className?: string;
  animation?: SkeletonAnimation;
}> = ({ className, animation = 'pulse' }) => (
  <div className={cn('flex items-center justify-between p-4', className)}>
    {/* Logo */}
    <Skeleton variant="rounded" height={32} width={120} animation={animation} />

    {/* 导航菜单 */}
    <div className="flex space-x-6">
      {Array.from({ length: 4 }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          height={16}
          width={60}
          animation={animation}
        />
      ))}
    </div>

    {/* 用户菜单 */}
    <div className="flex items-center space-x-3">
      <Skeleton variant="rounded" height={32} width={80} animation={animation} />
      <AvatarSkeleton size="sm" animation={animation} />
    </div>
  </div>
);

// 仪表板骨架屏组件
export const DashboardSkeleton: React.FC<{
  className?: string;
  animation?: SkeletonAnimation;
}> = ({ className, animation = 'pulse' }) => (
  <div className={cn('space-y-6', className)}>
    {/* 导航栏 */}
    <NavigationSkeleton animation={animation} />

    {/* 页面标题 */}
    <div className="space-y-2">
      <Skeleton variant="text" height={32} width={300} animation={animation} />
      <Skeleton variant="text" height={16} width={500} animation={animation} />
    </div>

    {/* 统计卡片网格 */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow">
          <StatCardSkeleton animation={animation} />
        </div>
      ))}
    </div>

    {/* 主要内容区域 */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* 左侧内容 */}
      <div className="lg:col-span-2 space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <Skeleton variant="text" height={24} width={200} animation={animation} className="mb-4" />
          <TableSkeleton rows={6} columns={4} animation={animation} />
        </div>
      </div>

      {/* 右侧边栏 */}
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <Skeleton variant="text" height={20} width={150} animation={animation} className="mb-4" />
          <ListSkeleton items={4} animation={animation} />
        </div>
      </div>
    </div>
  </div>
);

export default Skeleton;
