'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useRouter } from 'next/navigation';

interface User {
  id: string;
  email: string;
  user_token: string;
  subscription_type: 'free' | 'pro' | 'enterprise';
}

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  lastCheck: number | null;
  sessionId: string | null;
}

interface AuthCache {
  user: User;
  timestamp: number;
  sessionId: string;
  verificationCount: number;
}

// 认证缓存配置
const AUTH_CACHE_CONFIG = {
  // 主缓存时间：30分钟
  PRIMARY_CACHE_DURATION: 30 * 60 * 1000,
  // 会话缓存时间：2小时
  SESSION_CACHE_DURATION: 2 * 60 * 60 * 1000,
  // 快速验证缓存时间：5分钟
  QUICK_CACHE_DURATION: 5 * 60 * 1000,
  // 防抖延迟
  DEBOUNCE_DELAY: 300,
  // 最大验证次数（防止频繁验证）
  MAX_VERIFICATION_COUNT: 3,
} as const;

// 生成会话ID
const generateSessionId = (): string => {
  return `auth_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 防抖函数
function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    lastCheck: null,
    sessionId: null,
  });
  const router = useRouter();
  const verificationInProgress = useRef(false);
  const authCacheRef = useRef<AuthCache | null>(null);

  // 智能缓存检查函数
  const checkCacheValidity = useCallback((cache: AuthCache, now: number): boolean => {
    // 检查主缓存是否有效
    if (now - cache.timestamp < AUTH_CACHE_CONFIG.PRIMARY_CACHE_DURATION) {
      return true;
    }

    // 检查会话缓存是否有效（更长时间，但需要验证次数限制）
    if (
      now - cache.timestamp < AUTH_CACHE_CONFIG.SESSION_CACHE_DURATION &&
      cache.verificationCount < AUTH_CACHE_CONFIG.MAX_VERIFICATION_COUNT
    ) {
      return true;
    }

    return false;
  }, []);

  // 从localStorage获取增强缓存数据
  const getEnhancedCacheData = useCallback(() => {
    try {
      const userToken = localStorage.getItem('userToken');
      const userData = localStorage.getItem('userData');
      const lastVerified = localStorage.getItem('lastVerified');
      const sessionId = localStorage.getItem('authSessionId');
      const verificationCount = localStorage.getItem('verificationCount');

      if (!userToken || !userData) {
        return null;
      }

      return {
        userToken,
        userData: JSON.parse(userData),
        lastVerified: lastVerified ? parseInt(lastVerified) : 0,
        sessionId: sessionId || generateSessionId(),
        verificationCount: verificationCount ? parseInt(verificationCount) : 0,
      };
    } catch (error) {
      console.error('Error reading cache data:', error);
      return null;
    }
  }, []);

  // 更新增强缓存数据
  const updateEnhancedCacheData = useCallback((
    user: User,
    incrementVerification: boolean = false
  ) => {
    try {
      const now = Date.now();
      const sessionId = authState.sessionId || generateSessionId();
      const currentCount = parseInt(localStorage.getItem('verificationCount') || '0');
      const newCount = incrementVerification ? currentCount + 1 : currentCount;

      localStorage.setItem('userData', JSON.stringify(user));
      localStorage.setItem('lastVerified', now.toString());
      localStorage.setItem('authSessionId', sessionId);
      localStorage.setItem('verificationCount', newCount.toString());

      // 更新内存缓存
      authCacheRef.current = {
        user,
        timestamp: now,
        sessionId,
        verificationCount: newCount,
      };

      setAuthState(prev => ({
        ...prev,
        user,
        isAuthenticated: true,
        isLoading: false,
        lastCheck: now,
        sessionId,
      }));
    } catch (error) {
      console.error('Error updating cache data:', error);
    }
  }, [authState.sessionId]);

  // 清除所有认证数据
  const clearAuthData = useCallback(() => {
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
    localStorage.removeItem('lastVerified');
    localStorage.removeItem('authSessionId');
    localStorage.removeItem('verificationCount');
    authCacheRef.current = null;

    setAuthState({
      user: null,
      isLoading: false,
      isAuthenticated: false,
      lastCheck: Date.now(),
      sessionId: null,
    });
  }, []);

  // 执行远程验证
  const performRemoteVerification = useCallback(async (userToken: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/user/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': 'our_api_key_v1_2024',
        },
        body: JSON.stringify({ userToken }),
      });

      if (response.ok) {
        const result = await response.json();
        return result.success;
      }
      return false;
    } catch (error) {
      console.error('Remote verification failed:', error);
      return false;
    }
  }, []);

  // 主要的认证检查函数
  const checkAuthStatus = useCallback(async (forceCheck: boolean = false) => {
    // 防止重复验证
    if (verificationInProgress.current && !forceCheck) {
      console.log('Auth verification already in progress, skipping...');
      return;
    }

    try {
      verificationInProgress.current = true;
      const now = Date.now();
      console.log('Starting auth check, forceCheck:', forceCheck);

      // 直接在函数内部获取缓存数据，避免依赖外部函数
      const cacheData = (() => {
        try {
          const userToken = localStorage.getItem('userToken');
          const userData = localStorage.getItem('userData');
          const lastVerified = localStorage.getItem('lastVerified');
          const sessionId = localStorage.getItem('authSessionId');
          const verificationCount = localStorage.getItem('verificationCount');

          if (!userToken || !userData) {
            return null;
          }

          return {
            userToken,
            userData: JSON.parse(userData),
            lastVerified: lastVerified ? parseInt(lastVerified) : 0,
            sessionId: sessionId || generateSessionId(),
            verificationCount: verificationCount ? parseInt(verificationCount) : 0,
          };
        } catch (error) {
          console.error('Error reading cache data:', error);
          return null;
        }
      })();

      if (!cacheData) {
        // 清除认证数据
        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
        localStorage.removeItem('lastVerified');
        localStorage.removeItem('authSessionId');
        localStorage.removeItem('verificationCount');
        authCacheRef.current = null;

        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
          lastCheck: now,
          sessionId: null,
        });
        return;
      }

      const { userToken, userData, lastVerified, sessionId, verificationCount } = cacheData;

      // 构建缓存对象
      const cache: AuthCache = {
        user: userData,
        timestamp: lastVerified,
        sessionId,
        verificationCount,
      };

      // 检查缓存有效性
      const isCacheValid = (() => {
        // 检查主缓存是否有效
        if (now - cache.timestamp < AUTH_CACHE_CONFIG.PRIMARY_CACHE_DURATION) {
          return true;
        }

        // 检查会话缓存是否有效（更长时间，但需要验证次数限制）
        if (
          now - cache.timestamp < AUTH_CACHE_CONFIG.SESSION_CACHE_DURATION &&
          cache.verificationCount < AUTH_CACHE_CONFIG.MAX_VERIFICATION_COUNT
        ) {
          return true;
        }

        return false;
      })();

      if (!forceCheck && isCacheValid) {
        // 使用缓存数据
        authCacheRef.current = cache;
        setAuthState({
          user: userData,
          isLoading: false,
          isAuthenticated: true,
          lastCheck: now,
          sessionId,
        });
        return;
      }

      // 需要远程验证
      setAuthState(prev => ({ ...prev, isLoading: true }));

      // 执行远程验证
      try {
        const response = await fetch('/api/user/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': 'our_api_key_v1_2024',
          },
          body: JSON.stringify({ userToken }),
        });

        if (response.ok) {
          const result = await response.json();
          const isValid = result.success;

          if (isValid) {
            // 验证成功，更新缓存
            const newSessionId = authState.sessionId || generateSessionId();
            const currentCount = parseInt(localStorage.getItem('verificationCount') || '0');
            const newCount = currentCount + 1;

            localStorage.setItem('userData', JSON.stringify(userData));
            localStorage.setItem('lastVerified', now.toString());
            localStorage.setItem('authSessionId', newSessionId);
            localStorage.setItem('verificationCount', newCount.toString());

            // 更新内存缓存
            authCacheRef.current = {
              user: userData,
              timestamp: now,
              sessionId: newSessionId,
              verificationCount: newCount,
            };

            setAuthState({
              user: userData,
              isAuthenticated: true,
              isLoading: false,
              lastCheck: now,
              sessionId: newSessionId,
            });
          } else {
            // 验证失败，清除数据
            localStorage.removeItem('userToken');
            localStorage.removeItem('userData');
            localStorage.removeItem('lastVerified');
            localStorage.removeItem('authSessionId');
            localStorage.removeItem('verificationCount');
            authCacheRef.current = null;

            setAuthState({
              user: null,
              isLoading: false,
              isAuthenticated: false,
              lastCheck: now,
              sessionId: null,
            });
          }
        } else {
          throw new Error('Verification request failed');
        }
      } catch (verifyError) {
        console.error('Remote verification failed:', verifyError);
        // 验证失败，清除数据
        localStorage.removeItem('userToken');
        localStorage.removeItem('userData');
        localStorage.removeItem('lastVerified');
        localStorage.removeItem('authSessionId');
        localStorage.removeItem('verificationCount');
        authCacheRef.current = null;

        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
          lastCheck: now,
          sessionId: null,
        });
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      // 清除数据
      localStorage.removeItem('userToken');
      localStorage.removeItem('userData');
      localStorage.removeItem('lastVerified');
      localStorage.removeItem('authSessionId');
      localStorage.removeItem('verificationCount');
      authCacheRef.current = null;

      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
        lastCheck: Date.now(),
        sessionId: null,
      });
    } finally {
      verificationInProgress.current = false;
    }
  }, []); // 移除所有依赖，避免无限循环

  // 防抖的认证检查
  const debouncedAuthCheck = useMemo(
    () => debounce(checkAuthStatus, AUTH_CACHE_CONFIG.DEBOUNCE_DELAY),
    [checkAuthStatus]
  );

  // 初始化认证状态 - 只在组件挂载时执行一次
  useEffect(() => {
    checkAuthStatus();
  }, []); // 移除依赖，只在挂载时执行

  // 优化的登录函数
  const login = useCallback(async (email: string) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 保存用户Token
          localStorage.setItem('userToken', result.token);

          // 直接更新缓存数据
          const now = Date.now();
          const newSessionId = generateSessionId();

          localStorage.setItem('userData', JSON.stringify(result.user));
          localStorage.setItem('lastVerified', now.toString());
          localStorage.setItem('authSessionId', newSessionId);
          localStorage.setItem('verificationCount', '0');

          // 更新内存缓存
          authCacheRef.current = {
            user: result.user,
            timestamp: now,
            sessionId: newSessionId,
            verificationCount: 0,
          };

          // 更新状态
          setAuthState({
            user: result.user,
            isAuthenticated: true,
            isLoading: false,
            lastCheck: now,
            sessionId: newSessionId,
          });

          // 自动跳转到管理面板
          router.push('/dashboard');
          return { success: true };
        } else {
          setAuthState(prev => ({ ...prev, isLoading: false }));
          return { success: false, error: result.error || '登录失败' };
        }
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return { success: false, error: '网络请求失败' };
      }
    } catch (error) {
      console.error('Login failed:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return { success: false, error: '登录过程中发生错误' };
    }
  }, [router]);

  // 优化的登出函数
  const logout = useCallback(() => {
    // 清除所有认证相关数据
    localStorage.removeItem('userToken');
    localStorage.removeItem('userData');
    localStorage.removeItem('lastVerified');
    localStorage.removeItem('authSessionId');
    localStorage.removeItem('verificationCount');
    authCacheRef.current = null;

    setAuthState({
      user: null,
      isLoading: false,
      isAuthenticated: false,
      lastCheck: Date.now(),
      sessionId: null,
    });

    // 重定向到登录页面
    router.push('/login');
  }, [router]);

  // 获取用户Token
  const getUserToken = useCallback(() => {
    return localStorage.getItem('userToken');
  }, []);

  // 强制刷新认证状态
  const refreshAuth = useCallback(() => {
    debouncedAuthCheck(true);
  }, [debouncedAuthCheck]);

  // 获取缓存统计信息（用于调试）
  const getCacheStats = useCallback(() => {
    const cache = authCacheRef.current;
    if (!cache) return null;

    const now = Date.now();
    const age = now - cache.timestamp;
    const remainingTime = AUTH_CACHE_CONFIG.PRIMARY_CACHE_DURATION - age;

    return {
      cacheAge: age,
      remainingTime: Math.max(0, remainingTime),
      verificationCount: cache.verificationCount,
      sessionId: cache.sessionId,
      isValid: checkCacheValidity(cache, now),
    };
  }, [checkCacheValidity]);

  return {
    ...authState,
    login,
    logout,
    getUserToken,
    refreshAuth,
    getCacheStats,
    // 暴露防抖的认证检查函数供外部使用
    checkAuth: debouncedAuthCheck,
  };
}

// 优化的自动重定向Hook
export function useAuthRedirect() {
  const { isAuthenticated, isLoading, lastCheck } = useAuth();
  const router = useRouter();
  const redirectInProgress = useRef(false);

  // 防抖的重定向函数
  const performRedirect = useCallback(
    debounce((path: string) => {
      if (!redirectInProgress.current) {
        redirectInProgress.current = true;
        router.push(path);
        // 重置重定向标志
        setTimeout(() => {
          redirectInProgress.current = false;
        }, 1000);
      }
    }, 100),
    [router]
  );

  useEffect(() => {
    // 只在认证状态确定且不在加载中时执行重定向
    if (!isLoading && lastCheck) {
      const currentPath = window.location.pathname;

      if (isAuthenticated) {
        // 已登录用户访问登录页面时，重定向到管理面板
        if (currentPath === '/login' || currentPath === '/') {
          performRedirect('/dashboard');
        }
      } else {
        // 未登录用户访问受保护页面时，重定向到登录页面
        const protectedPaths = ['/dashboard', '/stats', '/settings'];
        if (protectedPaths.some(path => currentPath.startsWith(path))) {
          performRedirect('/login');
        }
      }
    }
  }, [isAuthenticated, isLoading, lastCheck, performRedirect]);

  return {
    isAuthenticated,
    isLoading,
    lastCheck,
    // 提供手动重定向功能
    redirectTo: performRedirect,
  };
}
