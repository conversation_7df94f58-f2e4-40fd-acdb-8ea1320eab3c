/**
 * API工具类
 * 确保与VSCode插件100%兼容的API响应格式
 */

import { NextRequest } from 'next/server';
import type { 
  UserVerifyResponse, 
  AvailableTokensResponse, 
  TokenUpdateResponse,
  ApiError 
} from '@/types';

export class ApiUtils {
  /**
   * 从请求中提取Bearer Token
   */
  static extractBearerToken(request: NextRequest): string | null {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7); // 移除 "Bearer " 前缀
  }

  /**
   * 从请求中提取API Key
   */
  static extractApiKey(request: NextRequest): string | null {
    return request.headers.get('X-API-Key');
  }

  /**
   * 验证API Key
   */
  static validateApiKey(apiKey: string | null): boolean {
    const validApiKeys = [
      'augment_external_v1_2024', // 兼容现有插件
      'our_api_key_v1_2024',      // 我们的新API Key
      process.env.API_SECRET_KEY   // 环境变量中的密钥
    ];
    
    return validApiKeys.includes(apiKey || '');
  }

  /**
   * 获取客户端IP地址
   */
  static getClientIP(request: NextRequest): string {
    // 尝试多种方式获取真实IP
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const cfConnectingIP = request.headers.get('cf-connecting-ip');
    
    if (cfConnectingIP) return cfConnectingIP;
    if (realIP) return realIP;
    if (forwarded) return forwarded.split(',')[0].trim();
    
    return 'unknown';
  }

  /**
   * 获取User-Agent
   */
  static getUserAgent(request: NextRequest): string {
    return request.headers.get('User-Agent') || 'unknown';
  }

  /**
   * 创建标准的成功响应
   */
  static createSuccessResponse<T>(data: T, status: number = 200): Response {
    return Response.json(data, { 
      status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });
  }

  /**
   * 创建标准的错误响应
   */
  static createErrorResponse(
    error: string, 
    status: number = 400, 
    redirect?: string
  ): Response {
    const errorResponse: ApiError = { error };
    if (redirect) {
      errorResponse.redirect = redirect;
    }

    return Response.json(errorResponse, { 
      status,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });
  }

  /**
   * 创建用户验证响应（兼容VSCode插件）
   */
  static createUserVerifyResponse(
    success: boolean, 
    user?: { email: string; verified_at: string }, 
    error?: string
  ): Response {
    const response: UserVerifyResponse = { success };
    
    if (user) {
      response.user = user;
    }
    
    if (error) {
      response.error = error;
    }

    return this.createSuccessResponse(response);
  }

  /**
   * 创建Token列表响应（兼容VSCode插件）
   */
  static createAvailableTokensResponse(
    success: boolean,
    tokens?: any[],
    error?: string
  ): Response {
    const response: AvailableTokensResponse = { success };
    
    if (tokens) {
      response.data = tokens;
    }
    
    if (error) {
      response.error = error;
    }

    return this.createSuccessResponse(response);
  }

  /**
   * 创建Token更新响应（兼容VSCode插件）
   */
  static createTokenUpdateResponse(
    success: boolean,
    message?: string,
    userCk?: string,
    error?: string
  ): Response {
    const response: TokenUpdateResponse = { success };
    
    if (message) {
      response.message = message;
    }
    
    if (userCk) {
      response.user_ck = userCk;
    }
    
    if (error) {
      response.error = error;
    }

    return this.createSuccessResponse(response);
  }

  /**
   * 处理CORS预检请求
   */
  static handleCORS(): Response {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
        'Access-Control-Max-Age': '86400',
      },
    });
  }

  /**
   * 验证请求参数
   */
  static validateRequiredFields(data: any, requiredFields: string[]): string | null {
    for (const field of requiredFields) {
      if (!data[field]) {
        return `缺少必需参数: ${field}`;
      }
    }
    return null;
  }

  /**
   * 安全地解析JSON请求体
   */
  static async safeParseJSON<T>(request: NextRequest): Promise<{ data: T | null; error: string | null }> {
    try {
      const text = await request.text();
      if (!text.trim()) {
        return { data: null, error: '请求体为空' };
      }
      
      const data = JSON.parse(text) as T;
      return { data, error: null };
    } catch (error) {
      return { data: null, error: 'JSON格式错误' };
    }
  }

  /**
   * 生成随机字符串
   */
  static generateRandomString(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成用户Token
   */
  static generateUserToken(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = this.generateRandomString(16);
    return `usr_${timestamp}_${randomStr}`;
  }

  /**
   * 验证邮箱格式
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 限流检查
   */
  static async checkRateLimit(
    identifier: string, 
    maxRequests: number = 100, 
    windowMs: number = 60000
  ): Promise<{ allowed: boolean; remaining: number }> {
    // 这里可以实现基于Redis或内存的限流逻辑
    // 暂时返回允许，后续可以集成Redis
    return { allowed: true, remaining: maxRequests };
  }

  /**
   * 记录API访问日志
   */
  static logApiAccess(
    endpoint: string,
    method: string,
    userToken?: string,
    ip?: string,
    userAgent?: string,
    responseStatus?: number
  ): void {
    const logData = {
      timestamp: new Date().toISOString(),
      endpoint,
      method,
      userToken: userToken ? `${userToken.substring(0, 8)}...` : 'anonymous',
      ip,
      userAgent,
      responseStatus
    };
    
    // 在生产环境中，这里应该写入日志系统
    console.log('API Access:', logData);
  }
}
