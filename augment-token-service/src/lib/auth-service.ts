/**
 * 用户认证服务
 * 提供完整的用户认证、授权和会话管理功能
 * 包含高性能缓存层和轻量级验证机制
 */

import { supabaseAdmin, DatabaseUtils, ErrorHandler } from './supabase';
import { ApiUtils } from './api-utils';
import { AuthCacheService } from './cache-service';
import type { User, LoginRequest, LoginResponse, UserVerifyRequest, UserVerifyResponse } from '@/types';

// 认证缓存接口
interface AuthCacheEntry {
  user: User;
  timestamp: number;
  lastActivity: number;
  verificationCount: number;
  permissions?: {
    canAccessTokens: boolean;
    dailyLimit: number;
    remainingRequests: number;
  };
}

// 轻量级用户验证响应
interface QuickVerifyResponse {
  success: boolean;
  user: User | null;
  error: string | null;
  fromCache: boolean;
}

// 认证缓存配置
const AUTH_CACHE_CONFIG = {
  // 用户基本信息缓存时间：15分钟
  USER_CACHE_TTL: 15 * 60 * 1000,
  // 权限信息缓存时间：5分钟
  PERMISSIONS_CACHE_TTL: 5 * 60 * 1000,
  // 最大缓存条目数
  MAX_CACHE_SIZE: 1000,
  // 缓存清理间隔：30分钟
  CACHE_CLEANUP_INTERVAL: 30 * 60 * 1000,
} as const;

/**
 * 高性能认证缓存管理器
 */
class AuthCache {
  private static instance: AuthCache;
  private cache = new Map<string, AuthCacheEntry>();
  private cleanupTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.startCleanupTimer();
  }

  static getInstance(): AuthCache {
    if (!AuthCache.instance) {
      AuthCache.instance = new AuthCache();
    }
    return AuthCache.instance;
  }

  /**
   * 获取缓存的用户信息
   */
  getUser(userToken: string): AuthCacheEntry | null {
    const entry = this.cache.get(userToken);
    if (!entry) return null;

    const now = Date.now();

    // 检查用户信息是否过期
    if (now - entry.timestamp > AUTH_CACHE_CONFIG.USER_CACHE_TTL) {
      this.cache.delete(userToken);
      return null;
    }

    // 更新最后活动时间
    entry.lastActivity = now;
    return entry;
  }

  /**
   * 设置用户缓存
   */
  setUser(userToken: string, user: User, permissions?: any): void {
    const now = Date.now();

    // 检查缓存大小限制
    if (this.cache.size >= AUTH_CACHE_CONFIG.MAX_CACHE_SIZE) {
      this.evictOldestEntries();
    }

    const entry: AuthCacheEntry = {
      user,
      timestamp: now,
      lastActivity: now,
      verificationCount: (this.cache.get(userToken)?.verificationCount || 0) + 1,
      permissions,
    };

    this.cache.set(userToken, entry);
  }

  /**
   * 检查权限缓存是否有效
   */
  isPermissionsCacheValid(entry: AuthCacheEntry): boolean {
    if (!entry.permissions) return false;

    const now = Date.now();
    return (now - entry.timestamp) < AUTH_CACHE_CONFIG.PERMISSIONS_CACHE_TTL;
  }

  /**
   * 更新权限缓存
   */
  updatePermissions(userToken: string, permissions: any): void {
    const entry = this.cache.get(userToken);
    if (entry) {
      entry.permissions = permissions;
      entry.timestamp = Date.now();
    }
  }

  /**
   * 删除用户缓存
   */
  removeUser(userToken: string): void {
    this.cache.delete(userToken);
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number;
    entries: Array<{
      token: string;
      age: number;
      verificationCount: number;
      hasPermissions: boolean;
    }>;
  } {
    const now = Date.now();
    const entries = Array.from(this.cache.entries()).map(([token, entry]) => ({
      token: token.substring(0, 8) + '...',
      age: now - entry.timestamp,
      verificationCount: entry.verificationCount,
      hasPermissions: !!entry.permissions,
    }));

    return {
      size: this.cache.size,
      entries,
    };
  }

  /**
   * 清理过期条目
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredTokens: string[] = [];

    for (const [token, entry] of this.cache.entries()) {
      if (now - entry.timestamp > AUTH_CACHE_CONFIG.USER_CACHE_TTL) {
        expiredTokens.push(token);
      }
    }

    expiredTokens.forEach(token => this.cache.delete(token));

    if (expiredTokens.length > 0) {
      console.log(`AuthCache: Cleaned up ${expiredTokens.length} expired entries`);
    }
  }

  /**
   * 驱逐最旧的条目
   */
  private evictOldestEntries(): void {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].lastActivity - b[1].lastActivity);

    // 删除最旧的25%条目
    const toDelete = Math.floor(entries.length * 0.25);
    for (let i = 0; i < toDelete; i++) {
      this.cache.delete(entries[i][0]);
    }

    console.log(`AuthCache: Evicted ${toDelete} oldest entries`);
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, AUTH_CACHE_CONFIG.CACHE_CLEANUP_INTERVAL);
  }

  /**
   * 停止清理定时器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.cache.clear();
  }
}

export class AuthService {
  private static authCache = AuthCache.getInstance();
  private static cacheService = AuthCacheService.getInstance();

  /**
   * 轻量级用户验证（高性能版本）
   * 优先使用缓存，减少数据库查询
   */
  static async quickVerifyUser(userToken: string): Promise<QuickVerifyResponse> {
    try {
      if (!userToken) {
        return {
          success: false,
          user: null,
          error: '未提供用户Token',
          fromCache: false,
        };
      }

      // 首先检查新缓存服务
      const cachedUser = this.cacheService.getUser(userToken);
      if (cachedUser) {
        return {
          success: true,
          user: cachedUser,
          error: null,
          fromCache: true,
        };
      }

      // 检查旧缓存（向后兼容）
      const cachedEntry = this.authCache.getUser(userToken);
      if (cachedEntry) {
        // 迁移到新缓存
        this.cacheService.setUser(userToken, cachedEntry.user);
        return {
          success: true,
          user: cachedEntry.user,
          error: null,
          fromCache: true,
        };
      }

      // 缓存未命中，执行数据库查询
      const { user, error } = await this.getUserByToken(userToken);

      if (error || !user) {
        return {
          success: false,
          user: null,
          error: error || '用户不存在或未激活',
          fromCache: false,
        };
      }

      // 将结果存入新缓存服务
      this.cacheService.setUser(userToken, user);
      // 同时更新旧缓存（向后兼容）
      this.authCache.setUser(userToken, user);

      return {
        success: true,
        user,
        error: null,
        fromCache: false,
      };

    } catch (error) {
      console.error('AuthService.quickVerifyUser error:', error);
      return {
        success: false,
        user: null,
        error: '验证服务异常',
        fromCache: false,
      };
    }
  }

  /**
   * 轻量级权限检查（仅检查用户存在性，不检查使用限制）
   */
  static async quickCheckPermissions(userToken: string): Promise<{
    user: User | null;
    error: string | null;
    fromCache: boolean;
  }> {
    const verifyResult = await this.quickVerifyUser(userToken);

    return {
      user: verifyResult.user,
      error: verifyResult.error,
      fromCache: verifyResult.fromCache,
    };
  }

  /**
   * 获取缓存统计信息（用于监控和调试）
   */
  static getCacheStats(): {
    legacy: {
      size: number;
      entries: Array<{
        token: string;
        age: number;
        verificationCount: number;
        hasPermissions: boolean;
      }>;
    };
    modern: any;
  } {
    return {
      legacy: this.authCache.getStats(),
      modern: this.cacheService.getComprehensiveStats(),
    };
  }

  /**
   * 清除指定用户的缓存
   */
  static clearUserCache(userToken: string): void {
    this.authCache.removeUser(userToken);
    this.cacheService.deleteUser(userToken);
  }

  /**
   * 清除所有认证缓存
   */
  static clearAllCache(): void {
    this.authCache.clear();
    this.cacheService.clearAll();
  }

  /**
   * 预热缓存（批量加载常用用户）
   */
  static async warmupCache(userTokens: string[]): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const token of userTokens) {
      try {
        const result = await this.quickVerifyUser(token);
        if (result.success) {
          success++;
        } else {
          failed++;
          errors.push(`${token}: ${result.error}`);
        }
      } catch (error) {
        failed++;
        errors.push(`${token}: ${error}`);
      }
    }

    return { success, failed, errors };
  }

  /**
   * 获取缓存健康状态
   */
  static getCacheHealth(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  } {
    const stats = this.cacheService.getComprehensiveStats();
    const issues: string[] = [];
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // 检查命中率
    if (stats.users.hitRate < 0.5) {
      issues.push('用户缓存命中率过低');
      recommendations.push('考虑增加缓存TTL或优化查询模式');
      status = 'warning';
    }

    if (stats.users.hitRate < 0.3) {
      status = 'critical';
    }

    // 检查内存使用
    if (stats.totalMemoryUsage > 1000) {
      issues.push('缓存内存使用过高');
      recommendations.push('考虑减少缓存大小或增加清理频率');
      if (status === 'healthy') status = 'warning';
    }

    if (issues.length === 0) {
      recommendations.push('缓存运行状态良好');
    }

    return { status, issues, recommendations };
  }

  /**
   * 用户登录服务
   * 支持邮箱登录，自动创建新用户
   */
  static async loginUser(loginData: LoginRequest): Promise<LoginResponse> {
    try {
      // 验证邮箱格式
      if (!ApiUtils.validateEmail(loginData.email)) {
        return {
          success: false,
          error: '邮箱格式不正确'
        };
      }

      // 创建或获取用户
      const { user, error } = await this.createOrGetUser(loginData.email);
      
      if (error || !user) {
        return {
          success: false,
          error: error || '用户创建失败'
        };
      }

      // 更新最后登录时间
      await this.updateUserLastLogin(user.id);

      // 生成会话Token（这里使用用户的user_token作为会话标识）
      return {
        success: true,
        token: user.user_token,
        user: {
          id: user.id,
          email: user.email,
          subscription_type: user.subscription_type
        },
        redirect: '/dashboard'
      };

    } catch (error) {
      console.error('AuthService.loginUser error:', error);
      return {
        success: false,
        error: '登录服务异常，请稍后重试'
      };
    }
  }

  /**
   * 验证用户Token（兼容VSCode插件，优化版本）
   */
  static async verifyUserToken(verifyData: UserVerifyRequest): Promise<UserVerifyResponse> {
    try {
      if (!verifyData.userToken) {
        return {
          success: false,
          error: '未提供用户Token'
        };
      }

      // 使用轻量级验证
      const quickVerifyResult = await this.quickVerifyUser(verifyData.userToken);

      if (!quickVerifyResult.success || !quickVerifyResult.user) {
        return {
          success: false,
          error: quickVerifyResult.error || '用户未激活或不存在'
        };
      }

      // 异步更新最后登录时间（不阻塞响应）
      this.updateUserLastLogin(quickVerifyResult.user.id).catch(error => {
        console.error('Failed to update last login time:', error);
      });

      return {
        success: true,
        user: {
          email: quickVerifyResult.user.email,
          verified_at: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('AuthService.verifyUserToken error:', error);
      return {
        success: false,
        error: '验证服务异常'
      };
    }
  }

  /**
   * 创建或获取用户
   */
  private static async createOrGetUser(email: string): Promise<{ user: User | null; error: string | null }> {
    if (!supabaseAdmin) {
      return { user: null, error: 'Database not configured' };
    }

    try {
      // 首先尝试获取现有用户
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('email', email)
        .eq('is_active', true)
        .single();

      if (existingUser) {
        return { user: existingUser, error: null };
      }

      // 创建新用户
      const userToken = ApiUtils.generateUserToken();
      const { data: newUser, error } = await supabaseAdmin
        .from('users')
        .insert({
          email,
          user_token: userToken,
          subscription_type: 'free',
          is_active: true,
          total_requests: 0,
          daily_limit: 10
        })
        .select()
        .single();

      if (error) {
        return { user: null, error: ErrorHandler.handleDatabaseError(error) };
      }

      return { user: newUser, error: null };
    } catch (error) {
      console.error('AuthService.createOrGetUser error:', error);
      return { user: null, error: '用户操作失败' };
    }
  }

  /**
   * 根据Token获取用户
   */
  private static async getUserByToken(userToken: string): Promise<{ user: User | null; error: string | null }> {
    if (!supabaseAdmin) {
      return { user: null, error: 'Database not configured' };
    }

    const result = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('users')
        .select('*')
        .eq('user_token', userToken)
        .eq('is_active', true)
        .limit(1);
    });

    // 处理查询结果
    if (result.error) {
      return { user: null, error: result.error };
    }

    // 检查是否找到用户
    if (!result.data || result.data.length === 0) {
      return { user: null, error: '用户不存在或未激活' };
    }

    return { user: result.data[0], error: null };
  }

  /**
   * 更新用户最后登录时间
   */
  private static async updateUserLastLogin(userId: string): Promise<void> {
    if (!supabaseAdmin) return;

    try {
      await supabaseAdmin
        .from('users')
        .update({ 
          last_login_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);
    } catch (error) {
      console.error('Failed to update last login time:', error);
      // 不抛出错误，因为这不是关键操作
    }
  }

  /**
   * 检查用户权限（优化版本，支持缓存）
   */
  static async checkUserPermissions(userToken: string): Promise<{
    user: User | null;
    permissions: {
      canAccessTokens: boolean;
      dailyLimit: number;
      remainingRequests: number;
    };
    error: string | null;
  }> {
    try {
      // 首先检查缓存中的用户信息
      const cachedEntry = this.authCache.getUser(userToken);
      let user: User | null = null;
      let fromCache = false;

      if (cachedEntry) {
        user = cachedEntry.user;
        fromCache = true;

        // 检查权限缓存是否有效
        if (this.authCache.isPermissionsCacheValid(cachedEntry) && cachedEntry.permissions) {
          return {
            user,
            permissions: cachedEntry.permissions,
            error: null,
          };
        }
      } else {
        // 缓存未命中，查询数据库
        const { user: dbUser, error } = await this.getUserByToken(userToken);

        if (error || !dbUser) {
          return {
            user: null,
            permissions: {
              canAccessTokens: false,
              dailyLimit: 0,
              remainingRequests: 0
            },
            error: error || '用户不存在'
          };
        }

        user = dbUser;
      }

      // 检查每日使用限制（这部分需要实时查询）
      const { allowed, remaining } = await this.checkDailyUsageLimit(user.id, user.subscription_type);

      const permissions = {
        canAccessTokens: allowed,
        dailyLimit: this.getDailyLimit(user.subscription_type),
        remainingRequests: remaining
      };

      // 更新缓存
      if (fromCache && cachedEntry) {
        this.authCache.updatePermissions(userToken, permissions);
      } else {
        this.authCache.setUser(userToken, user, permissions);
      }

      return {
        user,
        permissions,
        error: null
      };

    } catch (error) {
      console.error('AuthService.checkUserPermissions error:', error);
      return {
        user: null,
        permissions: {
          canAccessTokens: false,
          dailyLimit: 0,
          remainingRequests: 0
        },
        error: '权限检查失败'
      };
    }
  }

  /**
   * 检查用户每日使用限制
   */
  private static async checkDailyUsageLimit(
    userId: string, 
    subscriptionType: string
  ): Promise<{ allowed: boolean; remaining: number }> {
    if (!supabaseAdmin) {
      return { allowed: false, remaining: 0 };
    }

    try {
      const dailyLimit = this.getDailyLimit(subscriptionType);
      
      if (dailyLimit === -1) {
        return { allowed: true, remaining: -1 }; // 无限制
      }

      // 使用数据库函数获取今日使用量
      const { data: usageToday } = await supabaseAdmin.rpc('get_user_daily_usage', {
        user_id: userId
      });

      const todayUsage = usageToday || 0;
      const remaining = Math.max(0, dailyLimit - todayUsage);

      return { 
        allowed: remaining > 0, 
        remaining 
      };
    } catch (error) {
      console.error('AuthService.checkDailyUsageLimit error:', error);
      return { allowed: false, remaining: 0 };
    }
  }

  /**
   * 获取订阅类型对应的每日限制
   */
  private static getDailyLimit(subscriptionType: string): number {
    const limits = {
      'free': 10,
      'pro': 1000,
      'enterprise': -1 // 无限制
    };
    
    return limits[subscriptionType as keyof typeof limits] || 10;
  }

  /**
   * 记录用户活动
   */
  static async recordUserActivity(
    userId: string,
    activityType: string,
    metadata?: any
  ): Promise<void> {
    // 这里可以记录用户活动日志
    // 暂时只在控制台输出，后续可以扩展到专门的日志表
    console.log('User activity:', {
      userId,
      activityType,
      timestamp: new Date().toISOString(),
      metadata
    });
  }

  /**
   * 升级用户订阅
   */
  static async upgradeUserSubscription(
    userId: string, 
    newSubscriptionType: 'pro' | 'enterprise'
  ): Promise<{ success: boolean; error?: string }> {
    if (!supabaseAdmin) {
      return { success: false, error: 'Database not configured' };
    }

    try {
      const { error } = await supabaseAdmin
        .from('users')
        .update({ 
          subscription_type: newSubscriptionType,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        return { success: false, error: ErrorHandler.handleDatabaseError(error) };
      }

      return { success: true };
    } catch (error) {
      console.error('AuthService.upgradeUserSubscription error:', error);
      return { success: false, error: '订阅升级失败' };
    }
  }
}
