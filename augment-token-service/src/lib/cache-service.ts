/**
 * 高性能缓存服务
 * 提供多层缓存机制，支持TTL、LRU淘汰策略和缓存统计
 */

import type { User } from '@/types';

// 缓存条目接口
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  lastAccess: number;
  accessCount: number;
  ttl: number;
}

// 缓存统计接口
interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: number;
  totalRequests: number;
  averageAge: number;
  oldestEntry: number;
  newestEntry: number;
}

// 缓存配置
interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
  enableStats: boolean;
}

/**
 * 通用高性能缓存类
 */
export class HighPerformanceCache<T = any> {
  private cache = new Map<string, CacheEntry<T>>();
  private stats = {
    hits: 0,
    misses: 0,
    totalRequests: 0,
  };
  private cleanupTimer: NodeJS.Timeout | null = null;
  private config: CacheConfig;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: config.maxSize || 1000,
      defaultTTL: config.defaultTTL || 15 * 60 * 1000, // 15分钟
      cleanupInterval: config.cleanupInterval || 5 * 60 * 1000, // 5分钟
      enableStats: config.enableStats !== false,
    };

    this.startCleanupTimer();
  }

  /**
   * 获取缓存数据
   */
  get(key: string): T | null {
    this.stats.totalRequests++;

    const entry = this.cache.get(key);
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    const now = Date.now();
    
    // 检查是否过期
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // 更新访问信息
    entry.lastAccess = now;
    entry.accessCount++;
    this.stats.hits++;

    return entry.data;
  }

  /**
   * 设置缓存数据
   */
  set(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    
    // 检查缓存大小限制
    if (this.cache.size >= this.config.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      lastAccess: now,
      accessCount: 1,
      ttl: ttl || this.config.defaultTTL,
    };

    this.cache.set(key, entry);
  }

  /**
   * 删除缓存条目
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * 检查缓存是否存在且有效
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    this.resetStats();
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const now = Date.now();
    const entries = Array.from(this.cache.values());
    
    const ages = entries.map(entry => now - entry.timestamp);
    const averageAge = ages.length > 0 ? ages.reduce((a, b) => a + b, 0) / ages.length : 0;
    const oldestEntry = ages.length > 0 ? Math.max(...ages) : 0;
    const newestEntry = ages.length > 0 ? Math.min(...ages) : 0;

    return {
      size: this.cache.size,
      hits: this.stats.hits,
      misses: this.stats.misses,
      hitRate: this.stats.totalRequests > 0 ? this.stats.hits / this.stats.totalRequests : 0,
      totalRequests: this.stats.totalRequests,
      averageAge,
      oldestEntry,
      newestEntry,
    };
  }

  /**
   * 获取所有缓存键
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * LRU淘汰策略
   */
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestAccess = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccess < oldestAccess) {
        oldestAccess = entry.lastAccess;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 清理过期条目
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`Cache cleanup: Removed ${expiredKeys.length} expired entries`);
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 重置统计信息
   */
  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      totalRequests: 0,
    };
  }

  /**
   * 销毁缓存实例
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.cache.clear();
    this.resetStats();
  }
}

/**
 * 认证专用缓存服务
 */
export class AuthCacheService {
  private static instance: AuthCacheService;
  private userCache: HighPerformanceCache<User>;
  private permissionsCache: HighPerformanceCache<any>;
  private sessionCache: HighPerformanceCache<any>;

  private constructor() {
    // 用户信息缓存：15分钟TTL
    this.userCache = new HighPerformanceCache<User>({
      maxSize: 1000,
      defaultTTL: 15 * 60 * 1000,
      cleanupInterval: 5 * 60 * 1000,
    });

    // 权限信息缓存：5分钟TTL
    this.permissionsCache = new HighPerformanceCache({
      maxSize: 1000,
      defaultTTL: 5 * 60 * 1000,
      cleanupInterval: 2 * 60 * 1000,
    });

    // 会话信息缓存：30分钟TTL
    this.sessionCache = new HighPerformanceCache({
      maxSize: 500,
      defaultTTL: 30 * 60 * 1000,
      cleanupInterval: 10 * 60 * 1000,
    });
  }

  static getInstance(): AuthCacheService {
    if (!AuthCacheService.instance) {
      AuthCacheService.instance = new AuthCacheService();
    }
    return AuthCacheService.instance;
  }

  /**
   * 用户缓存操作
   */
  getUser(token: string): User | null {
    return this.userCache.get(token);
  }

  setUser(token: string, user: User): void {
    this.userCache.set(token, user);
  }

  deleteUser(token: string): void {
    this.userCache.delete(token);
    this.permissionsCache.delete(token);
    this.sessionCache.delete(token);
  }

  /**
   * 权限缓存操作
   */
  getPermissions(token: string): any | null {
    return this.permissionsCache.get(token);
  }

  setPermissions(token: string, permissions: any): void {
    this.permissionsCache.set(token, permissions);
  }

  /**
   * 会话缓存操作
   */
  getSession(token: string): any | null {
    return this.sessionCache.get(token);
  }

  setSession(token: string, session: any): void {
    this.sessionCache.set(token, session);
  }

  /**
   * 获取综合统计信息
   */
  getComprehensiveStats(): {
    users: CacheStats;
    permissions: CacheStats;
    sessions: CacheStats;
    totalMemoryUsage: number;
  } {
    return {
      users: this.userCache.getStats(),
      permissions: this.permissionsCache.getStats(),
      sessions: this.sessionCache.getStats(),
      totalMemoryUsage: this.userCache.size() + this.permissionsCache.size() + this.sessionCache.size(),
    };
  }

  /**
   * 清空所有缓存
   */
  clearAll(): void {
    this.userCache.clear();
    this.permissionsCache.clear();
    this.sessionCache.clear();
  }

  /**
   * 销毁缓存服务
   */
  destroy(): void {
    this.userCache.destroy();
    this.permissionsCache.destroy();
    this.sessionCache.destroy();
  }
}
