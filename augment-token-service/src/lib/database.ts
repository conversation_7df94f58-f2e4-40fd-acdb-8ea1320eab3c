/**
 * 数据库操作工具类
 * 提供高级的数据库操作和业务逻辑
 */

import { supabaseAdmin, DatabaseUtils, ErrorHandler } from './supabase';
import type { User, TokenPool, TokenUsage, TokenInfo, UsageStats } from '@/types';

export class UserService {
  /**
   * 根据邮箱创建或获取用户
   */
  static async createOrGetUser(email: string): Promise<{ user: User | null; error: string | null }> {
    if (!supabaseAdmin) {
      return { user: null, error: 'Database not configured' };
    }

    try {
      // 首先尝试获取现有用户
      const { data: existingUser } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('email', email)
        .eq('is_active', true)
        .single();

      if (existingUser) {
        return { user: existingUser, error: null };
      }

      // 创建新用户
      const userToken = this.generateUserToken();
      const { data: newUser, error } = await supabaseAdmin
        .from('users')
        .insert({
          email,
          user_token: userToken,
          subscription_type: 'free',
          is_active: true,
        })
        .select()
        .single();

      if (error) {
        return { user: null, error: ErrorHandler.handleDatabaseError(error) };
      }

      return { user: newUser, error: null };
    } catch (error) {
      console.error('UserService.createOrGetUser error:', error);
      return { user: null, error: '用户创建失败' };
    }
  }

  /**
   * 验证用户Token
   */
  static async verifyUserToken(userToken: string): Promise<{ user: User | null; error: string | null }> {
    if (!supabaseAdmin) {
      return { user: null, error: 'Database not configured' };
    }

    const result = await DatabaseUtils.safeQuery(async () => {
      return await supabaseAdmin!
        .from('users')
        .select('*')
        .eq('user_token', userToken)
        .eq('is_active', true)
        .single();
    });

    return { user: result.data, error: result.error };
  }

  /**
   * 更新用户最后登录时间
   */
  static async updateLastLogin(userId: string): Promise<void> {
    if (!supabaseAdmin) return;

    await supabaseAdmin
      .from('users')
      .update({ 
        last_login_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);
  }

  /**
   * 生成用户Token
   */
  private static generateUserToken(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2);
    return `usr_${timestamp}_${randomStr}`;
  }

  /**
   * 检查用户每日使用限制
   */
  static async checkDailyLimit(userId: string): Promise<{ allowed: boolean; remaining: number }> {
    if (!supabaseAdmin) {
      return { allowed: false, remaining: 0 };
    }

    try {
      // 获取用户信息
      const { data: user } = await supabaseAdmin
        .from('users')
        .select('subscription_type')
        .eq('id', userId)
        .single();

      if (!user) {
        return { allowed: false, remaining: 0 };
      }

      // 获取每日限制
      const limits = {
        free: 10,
        pro: 1000,
        enterprise: -1 // 无限制
      };

      const dailyLimit = limits[user.subscription_type as keyof typeof limits] || 10;
      
      if (dailyLimit === -1) {
        return { allowed: true, remaining: -1 };
      }

      // 查询今日使用量
      const today = new Date().toISOString().split('T')[0];
      const { data: usageToday } = await supabaseAdmin
        .from('token_usage')
        .select('request_count')
        .eq('user_id', userId)
        .gte('used_at', `${today}T00:00:00Z`)
        .lt('used_at', `${today}T23:59:59Z`);

      const totalUsageToday = usageToday?.reduce((sum, record) => sum + record.request_count, 0) || 0;
      const remaining = Math.max(0, dailyLimit - totalUsageToday);

      return { 
        allowed: remaining > 0, 
        remaining 
      };
    } catch (error) {
      console.error('UserService.checkDailyLimit error:', error);
      return { allowed: false, remaining: 0 };
    }
  }
}

export class TokenPoolService {
  /**
   * 获取健康且可用的Token列表（未分配或分配已过期）
   */
  static async getHealthyTokens(page: number = 1, limit: number = 50): Promise<TokenInfo[]> {
    if (!supabaseAdmin) return [];

    try {
      const offset = (page - 1) * limit;
      const now = new Date();

      const { data: tokens } = await supabaseAdmin
        .from('token_pool')
        .select('*')
        .eq('is_healthy', true)
        // 只返回未分配或分配已过期的Token
        .or(`allocated_to.is.null,allocation_expires_at.lt.${now.toISOString()}`)
        .order('created_at', { ascending: true })  // 按创建时间升序排序
        .order('usage_count', { ascending: true })
        .range(offset, offset + limit - 1);

      if (!tokens) return [];

      // 转换为插件兼容格式
      return tokens.map(token => ({
        id: token.id,
        accessToken: token.access_token,
        tenantURL: token.tenant_url,  // 使用驼峰命名兼容VSCode扩展
        use_time: token.usage_count,
        created_at: token.created_at,
        updated_at: token.updated_at
      }));
    } catch (error) {
      console.error('TokenPoolService.getHealthyTokens error:', error);
      return [];
    }
  }

  /**
   * 智能分配Token
   */
  static async allocateOptimalToken(userId: string): Promise<TokenInfo | null> {
    if (!supabaseAdmin) return null;

    try {
      // 获取用户最近使用的Token，避免重复分配
      const { data: recentUsage } = await supabaseAdmin
        .from('token_usage')
        .select('token_id')
        .eq('user_id', userId)
        .order('used_at', { ascending: false })
        .limit(5);

      const recentTokenIds = recentUsage?.map(u => u.token_id) || [];

      // 查询最优Token（排除最近使用的）
      let query = supabaseAdmin
        .from('token_pool')
        .select('*')
        .eq('is_healthy', true)
        .order('usage_count', { ascending: true })
        .order('last_used_at', { ascending: true, nullsFirst: true })
        .limit(1);

      if (recentTokenIds.length > 0) {
        query = query.not('id', 'in', `(${recentTokenIds.map(id => `'${id}'`).join(',')})`);
      }

      const { data: tokens } = await query;

      if (!tokens || tokens.length === 0) {
        // 如果没有可用Token，返回任意健康Token
        const fallbackTokens = await this.getHealthyTokens(1, 1);
        return fallbackTokens[0] || null;
      }

      const selectedToken = tokens[0];

      // 更新Token使用统计
      await this.updateTokenUsage(selectedToken.id);

      return {
        id: selectedToken.id,
        accessToken: selectedToken.access_token,
        tenant_url: selectedToken.tenant_url,
        use_time: selectedToken.usage_count + 1,
        created_at: selectedToken.created_at,
        updated_at: selectedToken.updated_at
      };
    } catch (error) {
      console.error('TokenPoolService.allocateOptimalToken error:', error);
      return null;
    }
  }

  /**
   * 更新Token使用统计
   */
  static async updateTokenUsage(tokenId: string): Promise<void> {
    if (!supabaseAdmin) return;

    await supabaseAdmin
      .from('token_pool')
      .update({ 
        usage_count: supabaseAdmin.rpc('increment_usage_count', { token_id: tokenId }),
        last_used_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', tokenId);
  }

  /**
   * 记录Token使用
   */
  static async recordTokenUsage(
    userId: string, 
    tokenId: string, 
    sessionId?: string,
    userAgent?: string,
    ipAddress?: string
  ): Promise<void> {
    if (!supabaseAdmin) return;

    await supabaseAdmin
      .from('token_usage')
      .insert({
        user_id: userId,
        token_id: tokenId,
        session_id: sessionId,
        user_agent: userAgent,
        ip_address: ipAddress,
        request_count: 1,
        success: true
      });
  }

  /**
   * 添加新Token到池中
   */
  static async addTokenToPool(
    accessToken: string,
    tenantUrl: string,
    source: 'oauth' | 'manual' | 'auto' = 'manual',
    refreshToken?: string
  ): Promise<{ success: boolean; error?: string }> {
    if (!supabaseAdmin) {
      return { success: false, error: 'Database not configured' };
    }

    try {
      // 检查Token是否已存在
      const { data: existing } = await supabaseAdmin
        .from('token_pool')
        .select('id')
        .eq('access_token', accessToken)
        .single();

      if (existing) {
        return { success: false, error: 'Token already exists' };
      }

      // 添加新Token
      const { error } = await supabaseAdmin
        .from('token_pool')
        .insert({
          access_token: accessToken,
          tenant_url: tenantUrl,
          refresh_token: refreshToken,
          source,
          is_healthy: true,
          usage_count: 0
        });

      if (error) {
        return { success: false, error: ErrorHandler.handleDatabaseError(error) };
      }

      return { success: true };
    } catch (error) {
      console.error('TokenPoolService.addTokenToPool error:', error);
      return { success: false, error: '添加Token失败' };
    }
  }
}
