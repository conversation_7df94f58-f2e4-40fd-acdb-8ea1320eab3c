/**
 * OAuth会话数据库操作
 * 管理PKCE授权会话的存储和检索
 */

import { supabase } from '@/lib/supabase';
import { OAuthSession } from '@/lib/pkce';

/**
 * 创建OAuth会话记录
 */
export async function createOAuthSessionRecord(session: OAuthSession) {
  console.log('Creating OAuth session record:', {
    user_id: session.user_id,
    state: session.state,
    expires_at: session.expires_at
  });

  const { data, error } = await supabase
    .from('oauth_sessions')
    .insert({
      user_id: parseInt(session.user_id), // 转换为整数
      state: session.state,
      code_verifier: session.code_verifier,
      code_challenge: session.code_challenge,
      expires_at: session.expires_at
    })
    .select()
    .single();

  if (error) {
    console.error('创建OAuth会话失败:', error);
    throw new Error(`创建OAuth会话失败: ${error.message}`);
  }

  console.log('OAuth session created successfully:', data?.id);
  return data;
}

/**
 * 根据state查找OAuth会话
 */
export async function findOAuthSessionByState(state: string): Promise<OAuthSession | null> {
  const { data, error } = await supabase
    .from('oauth_sessions')
    .select('*')
    .eq('state', state)
    .gt('expires_at', new Date().toISOString())
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // 没有找到记录
      return null;
    }
    console.error('查找OAuth会话失败:', error);
    throw new Error('查找OAuth会话失败');
  }

  return data;
}

/**
 * 删除OAuth会话（使用后清理）
 */
export async function deleteOAuthSession(state: string) {
  const { error } = await supabase
    .from('oauth_sessions')
    .delete()
    .eq('state', state);

  if (error) {
    console.error('删除OAuth会话失败:', error);
    // 不抛出错误，因为这不是关键操作
  }
}

/**
 * 清理过期的OAuth会话
 */
export async function cleanupExpiredOAuthSessions() {
  const { error } = await supabase
    .from('oauth_sessions')
    .delete()
    .lt('expires_at', new Date().toISOString());

  if (error) {
    console.error('清理过期OAuth会话失败:', error);
  }
}

/**
 * 获取用户的活跃OAuth会话数量
 */
export async function getUserActiveOAuthSessions(userId: string): Promise<number> {
  const { count, error } = await supabase
    .from('oauth_sessions')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', parseInt(userId))
    .gt('expires_at', new Date().toISOString());

  if (error) {
    console.error('获取用户OAuth会话数量失败:', error);
    return 0;
  }

  return count || 0;
}
