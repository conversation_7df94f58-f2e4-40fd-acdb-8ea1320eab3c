/**
 * OAuth授权服务
 * 处理Augment OAuth流程的核心逻辑
 */

import { supabaseAdmin } from './supabase';
import { SmartTokenManager } from './smart-token-manager';
import { ApiUtils } from './api-utils';

export class OAuthService {
  private static readonly AUGMENT_AUTH_BASE_URL = 'https://auth.augmentcode.com/oauth2';
  private static readonly AUGMENT_TOKEN_ENDPOINT = `${this.AUGMENT_AUTH_BASE_URL}/token`;
  private static readonly AUGMENT_AUTHORIZE_ENDPOINT = `${this.AUGMENT_AUTH_BASE_URL}/authorize`;

  /**
   * 生成OAuth授权URL
   */
  static async generateAuthUrl(baseUrl: string): Promise<{
    authorizeUrl: string;
    state: string;
    codeVerifier: string;
  }> {
    try {
      // 生成OAuth参数
      const state = ApiUtils.generateRandomString(32);
      const codeVerifier = ApiUtils.generateRandomString(128);
      const codeChallenge = await this.generateCodeChallenge(codeVerifier);

      // 构建授权URL参数
      const authParams = new URLSearchParams({
        response_type: 'code',
        client_id: process.env.AUGMENT_CLIENT_ID || 'default_client_id',
        redirect_uri: `${baseUrl}/api/callback`,
        scope: 'openid profile email augment:api',
        state: state,
        code_challenge: codeChallenge,
        code_challenge_method: 'S256',
        prompt: 'consent' // 强制显示授权页面
      });

      const authorizeUrl = `${this.AUGMENT_AUTHORIZE_ENDPOINT}?${authParams.toString()}`;

      return {
        authorizeUrl,
        state,
        codeVerifier
      };

    } catch (error) {
      console.error('OAuthService.generateAuthUrl error:', error);
      throw new Error('生成授权URL失败');
    }
  }

  /**
   * 使用授权码换取访问令牌
   */
  static async exchangeCodeForToken(
    code: string,
    state: string,
    redirectUri: string,
    codeVerifier?: string
  ): Promise<{
    success: boolean;
    accessToken?: string;
    refreshToken?: string;
    tenantUrl?: string;
    expiresIn?: number;
    error?: string;
  }> {
    try {
      // 构建Token请求参数
      const tokenParams = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: process.env.AUGMENT_CLIENT_ID || 'default_client_id',
        client_secret: process.env.AUGMENT_CLIENT_SECRET || 'default_client_secret',
        code: code,
        redirect_uri: redirectUri,
        state: state
      });

      // 如果有code_verifier，添加到请求中（PKCE）
      if (codeVerifier) {
        tokenParams.append('code_verifier', codeVerifier);
      }

      // 发送Token请求
      const tokenResponse = await fetch(this.AUGMENT_TOKEN_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'AugmentTokenService/1.0.0',
          'Accept': 'application/json'
        },
        body: tokenParams.toString()
      });

      // 检查响应状态
      if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        console.error('Token exchange failed:', {
          status: tokenResponse.status,
          statusText: tokenResponse.statusText,
          body: errorText
        });
        
        return {
          success: false,
          error: `Token exchange failed: ${tokenResponse.status} ${tokenResponse.statusText}`
        };
      }

      // 解析Token响应
      const tokenData = await tokenResponse.json();

      // 验证响应数据
      if (!tokenData.access_token) {
        console.error('Invalid token response:', tokenData);
        return {
          success: false,
          error: 'Invalid token response: missing access_token'
        };
      }

      // 提取租户URL（如果可用）
      let tenantUrl = 'https://d5.api.augmentcode.com/';
      if (tokenData.tenant_url) {
        tenantUrl = tokenData.tenant_url;
      } else if (tokenData.instance_url) {
        tenantUrl = tokenData.instance_url;
      }

      return {
        success: true,
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        tenantUrl: tenantUrl,
        expiresIn: tokenData.expires_in
      };

    } catch (error) {
      console.error('OAuthService.exchangeCodeForToken error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Token exchange failed'
      };
    }
  }

  /**
   * 刷新访问令牌
   */
  static async refreshAccessToken(refreshToken: string): Promise<{
    success: boolean;
    accessToken?: string;
    newRefreshToken?: string;
    expiresIn?: number;
    error?: string;
  }> {
    try {
      const refreshParams = new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: process.env.AUGMENT_CLIENT_ID || 'default_client_id',
        client_secret: process.env.AUGMENT_CLIENT_SECRET || 'default_client_secret',
        refresh_token: refreshToken
      });

      const refreshResponse = await fetch(this.AUGMENT_TOKEN_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'AugmentTokenService/1.0.0',
          'Accept': 'application/json'
        },
        body: refreshParams.toString()
      });

      if (!refreshResponse.ok) {
        const errorText = await refreshResponse.text();
        console.error('Token refresh failed:', errorText);
        return {
          success: false,
          error: `Token refresh failed: ${refreshResponse.status}`
        };
      }

      const refreshData = await refreshResponse.json();

      if (!refreshData.access_token) {
        return {
          success: false,
          error: 'Invalid refresh response: missing access_token'
        };
      }

      return {
        success: true,
        accessToken: refreshData.access_token,
        newRefreshToken: refreshData.refresh_token || refreshToken,
        expiresIn: refreshData.expires_in
      };

    } catch (error) {
      console.error('OAuthService.refreshAccessToken error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Token refresh failed'
      };
    }
  }

  /**
   * 批量刷新过期Token
   */
  static async refreshExpiredTokens(): Promise<{
    refreshed: number;
    failed: number;
    errors: string[];
  }> {
    if (!supabaseAdmin) {
      return { refreshed: 0, failed: 0, errors: ['Database not configured'] };
    }

    try {
      // 获取即将过期或已过期的Token
      const { data: expiredTokens } = await supabaseAdmin
        .from('token_pool')
        .select('*')
        .not('refresh_token', 'is', null)
        .or('expires_at.is.null,expires_at.lt.' + new Date(Date.now() + 60 * 60 * 1000).toISOString()) // 1小时内过期
        .eq('is_healthy', true);

      if (!expiredTokens || expiredTokens.length === 0) {
        return { refreshed: 0, failed: 0, errors: [] };
      }

      let refreshed = 0;
      let failed = 0;
      const errors: string[] = [];

      // 批量刷新Token
      for (const token of expiredTokens) {
        try {
          const refreshResult = await this.refreshAccessToken(token.refresh_token!);
          
          if (refreshResult.success) {
            // 更新数据库中的Token
            await supabaseAdmin
              .from('token_pool')
              .update({
                access_token: refreshResult.accessToken!,
                refresh_token: refreshResult.newRefreshToken!,
                expires_at: refreshResult.expiresIn 
                  ? new Date(Date.now() + refreshResult.expiresIn * 1000).toISOString()
                  : null,
                updated_at: new Date().toISOString(),
                is_healthy: true,
                error_count: 0
              })
              .eq('id', token.id);

            refreshed++;
          } else {
            // 标记Token为不健康
            await supabaseAdmin
              .from('token_pool')
              .update({
                is_healthy: false,
                error_count: (token.error_count || 0) + 1,
                updated_at: new Date().toISOString()
              })
              .eq('id', token.id);

            failed++;
            errors.push(`Token ${token.id}: ${refreshResult.error}`);
          }

          // 添加延迟避免频率限制
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          failed++;
          errors.push(`Token ${token.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      return { refreshed, failed, errors };

    } catch (error) {
      console.error('OAuthService.refreshExpiredTokens error:', error);
      return { 
        refreshed: 0, 
        failed: 0, 
        errors: [error instanceof Error ? error.message : 'Batch refresh failed'] 
      };
    }
  }

  /**
   * 生成PKCE code challenge
   */
  private static async generateCodeChallenge(codeVerifier: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(codeVerifier);
    const digest = await crypto.subtle.digest('SHA-256', data);
    
    // 转换为base64url格式
    return btoa(String.fromCharCode(...new Uint8Array(digest)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * 验证OAuth state参数
   */
  static validateState(receivedState: string, expectedState: string): boolean {
    return receivedState === expectedState;
  }

  /**
   * 启动定时Token刷新任务
   */
  static startTokenRefreshScheduler(): void {
    // 每小时检查一次过期Token
    setInterval(async () => {
      try {
        console.log('Starting token refresh scheduler...');
        const result = await this.refreshExpiredTokens();
        console.log(`Token refresh completed: ${result.refreshed} refreshed, ${result.failed} failed`);
        
        if (result.errors.length > 0) {
          console.warn('Token refresh errors:', result.errors);
        }
      } catch (error) {
        console.error('Token refresh scheduler failed:', error);
      }
    }, 60 * 60 * 1000); // 每小时执行一次

    console.log('Token refresh scheduler started');
  }
}

export default OAuthService;
