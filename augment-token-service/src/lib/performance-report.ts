/**
 * 性能测试报告生成器
 * 生成详细的性能分析报告
 */

interface PerformanceData {
  before: {
    averageResponseTime: number;
    cacheHitRate: number;
    databaseQueries: number;
    memoryUsage: number;
  };
  after: {
    averageResponseTime: number;
    cacheHitRate: number;
    databaseQueries: number;
    memoryUsage: number;
  };
  testResults: {
    authTest: any;
    statsTest: any;
    cacheTest: any;
    concurrentTest: any;
  };
}

interface OptimizationReport {
  summary: {
    overallImprovement: number;
    responseTimeImprovement: number;
    cacheHitRateImprovement: number;
    queryReduction: number;
    memoryOptimization: number;
  };
  details: {
    authPerformance: string;
    cacheEfficiency: string;
    databaseOptimization: string;
    frontendOptimization: string;
  };
  recommendations: string[];
  nextSteps: string[];
}

export class PerformanceReportGenerator {
  /**
   * 生成优化效果报告
   */
  static generateOptimizationReport(data: PerformanceData): OptimizationReport {
    const summary = this.calculateSummary(data);
    const details = this.generateDetails(data);
    const recommendations = this.generateRecommendations(data);
    const nextSteps = this.generateNextSteps(data);

    return {
      summary,
      details,
      recommendations,
      nextSteps,
    };
  }

  /**
   * 计算总体改进指标
   */
  private static calculateSummary(data: PerformanceData) {
    const responseTimeImprovement = this.calculateImprovement(
      data.before.averageResponseTime,
      data.after.averageResponseTime,
      true // 越小越好
    );

    const cacheHitRateImprovement = this.calculateImprovement(
      data.before.cacheHitRate,
      data.after.cacheHitRate,
      false // 越大越好
    );

    const queryReduction = this.calculateImprovement(
      data.before.databaseQueries,
      data.after.databaseQueries,
      true // 越小越好
    );

    const memoryOptimization = this.calculateImprovement(
      data.before.memoryUsage,
      data.after.memoryUsage,
      true // 越小越好
    );

    const overallImprovement = (
      responseTimeImprovement +
      cacheHitRateImprovement +
      queryReduction +
      memoryOptimization
    ) / 4;

    return {
      overallImprovement,
      responseTimeImprovement,
      cacheHitRateImprovement,
      queryReduction,
      memoryOptimization,
    };
  }

  /**
   * 计算改进百分比
   */
  private static calculateImprovement(before: number, after: number, lowerIsBetter: boolean): number {
    if (before === 0) return 0;
    
    const change = lowerIsBetter ? (before - after) / before : (after - before) / before;
    return Math.max(-100, Math.min(100, change * 100));
  }

  /**
   * 生成详细分析
   */
  private static generateDetails(data: PerformanceData) {
    const authPerformance = this.analyzeAuthPerformance(data);
    const cacheEfficiency = this.analyzeCacheEfficiency(data);
    const databaseOptimization = this.analyzeDatabaseOptimization(data);
    const frontendOptimization = this.analyzeFrontendOptimization(data);

    return {
      authPerformance,
      cacheEfficiency,
      databaseOptimization,
      frontendOptimization,
    };
  }

  /**
   * 分析认证性能
   */
  private static analyzeAuthPerformance(data: PerformanceData): string {
    const improvement = this.calculateImprovement(
      data.before.averageResponseTime,
      data.after.averageResponseTime,
      true
    );

    if (improvement > 50) {
      return `认证性能显著提升 ${improvement.toFixed(1)}%。轻量级验证和缓存机制大幅减少了认证延迟。`;
    } else if (improvement > 20) {
      return `认证性能良好提升 ${improvement.toFixed(1)}%。缓存策略有效减少了重复验证。`;
    } else if (improvement > 0) {
      return `认证性能有所改善 ${improvement.toFixed(1)}%。优化效果初步显现。`;
    } else {
      return `认证性能需要进一步优化。建议检查缓存配置和数据库查询。`;
    }
  }

  /**
   * 分析缓存效率
   */
  private static analyzeCacheEfficiency(data: PerformanceData): string {
    const hitRateImprovement = this.calculateImprovement(
      data.before.cacheHitRate,
      data.after.cacheHitRate,
      false
    );

    const currentHitRate = data.after.cacheHitRate * 100;

    if (currentHitRate > 80) {
      return `缓存命中率优秀 (${currentHitRate.toFixed(1)}%)，提升了 ${hitRateImprovement.toFixed(1)}%。多层缓存策略效果显著。`;
    } else if (currentHitRate > 60) {
      return `缓存命中率良好 (${currentHitRate.toFixed(1)}%)，提升了 ${hitRateImprovement.toFixed(1)}%。仍有优化空间。`;
    } else {
      return `缓存命中率偏低 (${currentHitRate.toFixed(1)}%)。建议调整缓存策略和TTL配置。`;
    }
  }

  /**
   * 分析数据库优化
   */
  private static analyzeDatabaseOptimization(data: PerformanceData): string {
    const queryReduction = this.calculateImprovement(
      data.before.databaseQueries,
      data.after.databaseQueries,
      true
    );

    if (queryReduction > 40) {
      return `数据库查询次数显著减少 ${queryReduction.toFixed(1)}%。索引优化和查询合并效果明显。`;
    } else if (queryReduction > 20) {
      return `数据库查询次数有效减少 ${queryReduction.toFixed(1)}%。优化的查询策略发挥作用。`;
    } else if (queryReduction > 0) {
      return `数据库查询次数略有减少 ${queryReduction.toFixed(1)}%。可以进一步优化查询逻辑。`;
    } else {
      return `数据库查询次数未明显改善。建议检查查询优化和索引配置。`;
    }
  }

  /**
   * 分析前端优化
   */
  private static analyzeFrontendOptimization(data: PerformanceData): string {
    const memoryOptimization = this.calculateImprovement(
      data.before.memoryUsage,
      data.after.memoryUsage,
      true
    );

    if (memoryOptimization > 20) {
      return `前端内存使用优化明显，减少了 ${memoryOptimization.toFixed(1)}%。组件优化和缓存管理效果良好。`;
    } else if (memoryOptimization > 10) {
      return `前端内存使用有所优化，减少了 ${memoryOptimization.toFixed(1)}%。继续保持优化策略。`;
    } else if (memoryOptimization > 0) {
      return `前端内存使用略有改善，减少了 ${memoryOptimization.toFixed(1)}%。可以进一步优化组件渲染。`;
    } else {
      return `前端内存使用需要关注。建议检查组件生命周期和内存泄漏。`;
    }
  }

  /**
   * 生成优化建议
   */
  private static generateRecommendations(data: PerformanceData): string[] {
    const recommendations: string[] = [];

    // 基于缓存命中率的建议
    if (data.after.cacheHitRate < 0.7) {
      recommendations.push('增加缓存TTL时间，提高缓存命中率');
      recommendations.push('实现更智能的缓存失效策略');
    }

    // 基于响应时间的建议
    if (data.after.averageResponseTime > 500) {
      recommendations.push('进一步优化数据库查询性能');
      recommendations.push('考虑实现API响应压缩');
    }

    // 基于数据库查询的建议
    if (data.after.databaseQueries > data.before.databaseQueries * 0.8) {
      recommendations.push('增加更多复合索引以优化查询');
      recommendations.push('实现查询结果的批量处理');
    }

    // 基于内存使用的建议
    if (data.after.memoryUsage > data.before.memoryUsage * 0.9) {
      recommendations.push('优化前端组件的内存管理');
      recommendations.push('实现更高效的状态管理');
    }

    // 通用建议
    recommendations.push('定期监控性能指标，持续优化');
    recommendations.push('建立性能基准测试，确保优化效果持续');

    return recommendations;
  }

  /**
   * 生成下一步行动计划
   */
  private static generateNextSteps(data: PerformanceData): string[] {
    const nextSteps: string[] = [];

    // 基于整体性能确定优先级
    const summary = this.calculateSummary(data);

    if (summary.overallImprovement < 30) {
      nextSteps.push('制定更激进的性能优化计划');
      nextSteps.push('分析性能瓶颈，重点优化关键路径');
    }

    if (summary.cacheHitRateImprovement < 20) {
      nextSteps.push('重新设计缓存架构，实现更高效的缓存策略');
    }

    if (summary.responseTimeImprovement < 40) {
      nextSteps.push('深入分析慢查询，优化数据库性能');
      nextSteps.push('考虑引入CDN和边缘缓存');
    }

    // 长期规划
    nextSteps.push('建立持续性能监控体系');
    nextSteps.push('制定性能优化的长期路线图');
    nextSteps.push('培训团队成员的性能优化技能');

    return nextSteps;
  }

  /**
   * 生成Markdown格式的报告
   */
  static generateMarkdownReport(report: OptimizationReport): string {
    const { summary, details, recommendations, nextSteps } = report;

    return `# 性能优化效果报告

## 📊 总体改进情况

- **整体性能提升**: ${summary.overallImprovement.toFixed(1)}%
- **响应时间改进**: ${summary.responseTimeImprovement.toFixed(1)}%
- **缓存命中率提升**: ${summary.cacheHitRateImprovement.toFixed(1)}%
- **数据库查询减少**: ${summary.queryReduction.toFixed(1)}%
- **内存使用优化**: ${summary.memoryOptimization.toFixed(1)}%

## 🔍 详细分析

### 认证性能
${details.authPerformance}

### 缓存效率
${details.cacheEfficiency}

### 数据库优化
${details.databaseOptimization}

### 前端优化
${details.frontendOptimization}

## 💡 优化建议

${recommendations.map(rec => `- ${rec}`).join('\n')}

## 🚀 下一步行动计划

${nextSteps.map(step => `1. ${step}`).join('\n')}

---

*报告生成时间: ${new Date().toLocaleString('zh-CN')}*
`;
  }
}

export default PerformanceReportGenerator;
