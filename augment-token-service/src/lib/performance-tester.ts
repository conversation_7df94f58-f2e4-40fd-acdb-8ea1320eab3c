/**
 * 性能测试工具
 * 用于测试和验证系统优化效果
 */

interface PerformanceMetrics {
  responseTime: number;
  cacheHit: boolean;
  queryCount: number;
  memoryUsage: number;
  timestamp: number;
}

interface TestResult {
  testName: string;
  metrics: PerformanceMetrics[];
  averageResponseTime: number;
  cacheHitRate: number;
  totalQueries: number;
  success: boolean;
  error?: string;
}

interface PerformanceTestConfig {
  iterations: number;
  concurrency: number;
  warmupIterations: number;
  timeout: number;
}

export class PerformanceTester {
  private config: PerformanceTestConfig;
  private results: TestResult[] = [];

  constructor(config: Partial<PerformanceTestConfig> = {}) {
    this.config = {
      iterations: config.iterations || 100,
      concurrency: config.concurrency || 10,
      warmupIterations: config.warmupIterations || 10,
      timeout: config.timeout || 5000,
    };
  }

  /**
   * 测试API响应时间
   */
  async testApiPerformance(
    testName: string,
    apiCall: () => Promise<any>,
    expectedCacheHit?: boolean
  ): Promise<TestResult> {
    console.log(`开始性能测试: ${testName}`);
    const metrics: PerformanceMetrics[] = [];
    let success = true;
    let error: string | undefined;

    try {
      // 预热阶段
      console.log(`预热阶段: ${this.config.warmupIterations} 次迭代`);
      for (let i = 0; i < this.config.warmupIterations; i++) {
        await apiCall();
      }

      // 正式测试
      console.log(`正式测试: ${this.config.iterations} 次迭代`);
      const startTime = Date.now();

      for (let i = 0; i < this.config.iterations; i++) {
        const iterationStart = performance.now();
        
        try {
          const response = await Promise.race([
            apiCall(),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Timeout')), this.config.timeout)
            )
          ]);

          const iterationEnd = performance.now();
          const responseTime = iterationEnd - iterationStart;

          // 检测缓存命中
          const cacheHit = this.detectCacheHit(response, expectedCacheHit);

          metrics.push({
            responseTime,
            cacheHit,
            queryCount: this.extractQueryCount(response),
            memoryUsage: this.getMemoryUsage(),
            timestamp: Date.now(),
          });

        } catch (err) {
          console.error(`迭代 ${i + 1} 失败:`, err);
          success = false;
          error = err instanceof Error ? err.message : 'Unknown error';
        }

        // 添加小延迟避免过载
        if (i < this.config.iterations - 1) {
          await this.sleep(10);
        }
      }

      const endTime = Date.now();
      console.log(`测试完成，总耗时: ${endTime - startTime}ms`);

    } catch (err) {
      success = false;
      error = err instanceof Error ? err.message : 'Test setup failed';
    }

    // 计算统计数据
    const averageResponseTime = metrics.length > 0 
      ? metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length 
      : 0;

    const cacheHitRate = metrics.length > 0 
      ? metrics.filter(m => m.cacheHit).length / metrics.length 
      : 0;

    const totalQueries = metrics.reduce((sum, m) => sum + m.queryCount, 0);

    const result: TestResult = {
      testName,
      metrics,
      averageResponseTime,
      cacheHitRate,
      totalQueries,
      success,
      error,
    };

    this.results.push(result);
    return result;
  }

  /**
   * 并发测试
   */
  async testConcurrentPerformance(
    testName: string,
    apiCall: () => Promise<any>
  ): Promise<TestResult> {
    console.log(`开始并发测试: ${testName} (并发数: ${this.config.concurrency})`);
    
    const promises: Promise<PerformanceMetrics>[] = [];
    const startTime = Date.now();

    for (let i = 0; i < this.config.concurrency; i++) {
      promises.push(this.runSingleConcurrentTest(apiCall));
    }

    try {
      const metrics = await Promise.all(promises);
      const endTime = Date.now();

      console.log(`并发测试完成，总耗时: ${endTime - startTime}ms`);

      const averageResponseTime = metrics.reduce((sum, m) => sum + m.responseTime, 0) / metrics.length;
      const cacheHitRate = metrics.filter(m => m.cacheHit).length / metrics.length;
      const totalQueries = metrics.reduce((sum, m) => sum + m.queryCount, 0);

      const result: TestResult = {
        testName,
        metrics,
        averageResponseTime,
        cacheHitRate,
        totalQueries,
        success: true,
      };

      this.results.push(result);
      return result;

    } catch (error) {
      const result: TestResult = {
        testName,
        metrics: [],
        averageResponseTime: 0,
        cacheHitRate: 0,
        totalQueries: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Concurrent test failed',
      };

      this.results.push(result);
      return result;
    }
  }

  /**
   * 运行单个并发测试
   */
  private async runSingleConcurrentTest(apiCall: () => Promise<any>): Promise<PerformanceMetrics> {
    const start = performance.now();
    
    try {
      const response = await apiCall();
      const end = performance.now();

      return {
        responseTime: end - start,
        cacheHit: this.detectCacheHit(response),
        queryCount: this.extractQueryCount(response),
        memoryUsage: this.getMemoryUsage(),
        timestamp: Date.now(),
      };
    } catch (error) {
      throw new Error(`Concurrent test iteration failed: ${error}`);
    }
  }

  /**
   * 检测缓存命中
   */
  private detectCacheHit(response: any, expected?: boolean): boolean {
    if (expected !== undefined) {
      return expected;
    }

    // 尝试从响应中检测缓存命中
    if (response && typeof response === 'object') {
      if (response.fromCache !== undefined) {
        return response.fromCache;
      }
      if (response.data && response.data.fromCache !== undefined) {
        return response.data.fromCache;
      }
      if (response.cacheHit !== undefined) {
        return response.cacheHit;
      }
    }

    return false;
  }

  /**
   * 提取查询数量
   */
  private extractQueryCount(response: any): number {
    if (response && typeof response === 'object') {
      if (response.queryCount !== undefined) {
        return response.queryCount;
      }
      if (response.data && response.data.queryCount !== undefined) {
        return response.data.queryCount;
      }
    }
    return 1; // 默认假设一个查询
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): number {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in window.performance) {
      return (window.performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * 等待指定时间
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成性能报告
   */
  generateReport(): {
    summary: {
      totalTests: number;
      successfulTests: number;
      failedTests: number;
      averageResponseTime: number;
      overallCacheHitRate: number;
    };
    details: TestResult[];
    recommendations: string[];
  } {
    const totalTests = this.results.length;
    const successfulTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - successfulTests;

    const allMetrics = this.results.flatMap(r => r.metrics);
    const averageResponseTime = allMetrics.length > 0 
      ? allMetrics.reduce((sum, m) => sum + m.responseTime, 0) / allMetrics.length 
      : 0;

    const overallCacheHitRate = allMetrics.length > 0 
      ? allMetrics.filter(m => m.cacheHit).length / allMetrics.length 
      : 0;

    const recommendations = this.generateRecommendations();

    return {
      summary: {
        totalTests,
        successfulTests,
        failedTests,
        averageResponseTime,
        overallCacheHitRate,
      },
      details: this.results,
      recommendations,
    };
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const allMetrics = this.results.flatMap(r => r.metrics);

    if (allMetrics.length === 0) {
      return ['无足够数据生成建议'];
    }

    const avgResponseTime = allMetrics.reduce((sum, m) => sum + m.responseTime, 0) / allMetrics.length;
    const cacheHitRate = allMetrics.filter(m => m.cacheHit).length / allMetrics.length;

    // 响应时间建议
    if (avgResponseTime > 1000) {
      recommendations.push('平均响应时间超过1秒，建议优化数据库查询或增加缓存');
    } else if (avgResponseTime > 500) {
      recommendations.push('平均响应时间较高，建议检查慢查询并优化');
    } else if (avgResponseTime < 100) {
      recommendations.push('响应时间优秀，系统性能良好');
    }

    // 缓存命中率建议
    if (cacheHitRate < 0.3) {
      recommendations.push('缓存命中率较低，建议增加缓存时间或优化缓存策略');
    } else if (cacheHitRate < 0.7) {
      recommendations.push('缓存命中率中等，有进一步优化空间');
    } else {
      recommendations.push('缓存命中率良好，缓存策略有效');
    }

    // 失败率建议
    const failureRate = this.results.filter(r => !r.success).length / this.results.length;
    if (failureRate > 0.1) {
      recommendations.push('测试失败率较高，建议检查系统稳定性');
    }

    return recommendations;
  }

  /**
   * 清除测试结果
   */
  clearResults(): void {
    this.results = [];
  }
}

export default PerformanceTester;
