/**
 * PKCE (Proof Key for Code Exchange) 工具函数
 * 用于OAuth2授权流程的安全增强
 */

import crypto from 'crypto';

/**
 * 生成code_verifier
 * 长度在43-128字符之间的随机字符串
 */
export function generateCodeVerifier(): string {
  return crypto.randomBytes(32).toString('base64url');
}

/**
 * 生成code_challenge
 * 对code_verifier进行SHA256哈希并BASE64URL编码
 */
export function generateCodeChallenge(verifier: string): string {
  return crypto.createHash('sha256').update(verifier).digest('base64url');
}

/**
 * 生成state参数
 * 用于防CSRF攻击的随机字符串，格式类似竞争对手
 */
export function generateState(userId: string): string {
  // 生成类似竞争对手的短state参数：TwBuwn2GK9o (11字符)
  const random = crypto.randomBytes(8).toString('base64url').substring(0, 11);
  return random;
}

/**
 * 验证state参数
 * 由于使用简化的state格式，验证逻辑改为检查数据库中的OAuth会话
 */
export function validateState(state: string): { isValid: boolean; userId?: string } {
  // 简单的格式验证：应该是11个字符的base64url字符串
  if (!state || state.length !== 11 || !/^[A-Za-z0-9_-]+$/.test(state)) {
    return { isValid: false };
  }

  // 实际的用户验证将在数据库查询中进行
  return { isValid: true };
}

/**
 * 构造Augment OAuth授权URL
 */
export function buildAugmentAuthUrl(params: {
  clientId: string;
  codeChallenge: string;
  state: string;
  redirectUri?: string;
}): string {
  const baseUrl = 'https://auth.augmentcode.com/authorize';
  const searchParams = new URLSearchParams({
    response_type: 'code',
    code_challenge: params.codeChallenge,
    client_id: params.clientId,
    state: params.state,
    prompt: 'login',
    ...(params.redirectUri && { redirect_uri: params.redirectUri })
  });
  
  return `${baseUrl}?${searchParams.toString()}`;
}

/**
 * OAuth会话数据接口
 */
export interface OAuthSession {
  id?: number;
  user_id: string;
  state: string;
  code_verifier: string;
  code_challenge: string;
  created_at?: string;
  expires_at: string;
}

/**
 * 创建OAuth会话
 */
export function createOAuthSession(userId: string): {
  session: OAuthSession;
  authUrl: string;
} {
  const codeVerifier = generateCodeVerifier();
  const codeChallenge = generateCodeChallenge(codeVerifier);
  const state = generateState(userId);
  
  // 设置30分钟后过期
  const expiresAt = new Date(Date.now() + 30 * 60 * 1000).toISOString();
  
  const session: OAuthSession = {
    user_id: userId,
    state,
    code_verifier: codeVerifier,
    code_challenge: codeChallenge,
    expires_at: expiresAt
  };
  
  const authUrl = buildAugmentAuthUrl({
    clientId: 'v', // 使用竞争对手的有效客户端ID
    codeChallenge,
    state
  });
  
  return { session, authUrl };
}
