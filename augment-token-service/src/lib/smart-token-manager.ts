/**
 * 智能Token管理器
 * 实现高级的Token分配、健康监控和自动维护功能
 */

import { supabaseAdmin } from './supabase';
import { TokenHealthMonitor } from './token-health-monitor';
import type { TokenPool, TokenInfo, User } from '@/types';

export class SmartTokenManager {
  private static readonly MIN_HEALTHY_TOKENS = 20; // 最小健康Token数量
  private static readonly MAX_DAILY_USAGE_PER_TOKEN = 800; // 每个Token每日最大使用量
  private static readonly TOKEN_COOLDOWN_HOURS = 2; // Token冷却时间（小时）

  /**
   * 智能分配最优Token
   * 考虑使用频率、健康状态、时间分布等因素
   */
  static async allocateOptimalToken(userId: string): Promise<{
    token: TokenInfo | null;
    error: string | null;
  }> {
    if (!supabaseAdmin) {
      return { token: null, error: 'Database not configured' };
    }

    try {
      // 1. 获取用户最近使用的Token（避免重复分配）
      const recentTokenIds = await this.getUserRecentTokens(userId, 5);

      // 2. 查询可用的Token
      const availableTokens = await this.getAvailableTokens(recentTokenIds);

      if (availableTokens.length === 0) {
        // 如果没有可用Token，尝试获取任意健康Token
        const fallbackTokens = await this.getFallbackTokens();
        if (fallbackTokens.length === 0) {
          return { token: null, error: '暂无可用Token，请稍后重试' };
        }
        availableTokens.push(...fallbackTokens);
      }

      // 3. 应用智能选择算法
      const selectedToken = this.selectOptimalToken(availableTokens);

      // 4. 更新Token使用统计
      await this.updateTokenUsageStats(selectedToken.id, userId);

      // 5. 转换为插件兼容格式
      const tokenInfo: TokenInfo = {
        id: selectedToken.id,
        accessToken: selectedToken.access_token,
        tenantURL: selectedToken.tenant_url,  // 使用驼峰命名兼容VSCode扩展
        use_time: selectedToken.usage_count + 1,
        created_at: selectedToken.created_at,
        updated_at: selectedToken.updated_at
      };

      return { token: tokenInfo, error: null };

    } catch (error) {
      console.error('SmartTokenManager.allocateOptimalToken error:', error);
      return { token: null, error: 'Token分配失败' };
    }
  }

  /**
   * 获取用户最近使用的Token ID列表
   */
  private static async getUserRecentTokens(userId: string, limit: number): Promise<string[]> {
    if (!supabaseAdmin) return [];

    try {
      const { data: recentUsage } = await supabaseAdmin
        .from('token_usage')
        .select('token_id')
        .eq('user_id', userId)
        .order('used_at', { ascending: false })
        .limit(limit);

      return recentUsage?.map(u => u.token_id) || [];
    } catch (error) {
      console.error('Failed to get user recent tokens:', error);
      return [];
    }
  }

  /**
   * 获取可用的Token列表（未分配或分配已过期的健康Token）
   */
  private static async getAvailableTokens(excludeTokenIds: string[]): Promise<TokenPool[]> {
    if (!supabaseAdmin) return [];

    try {
      const now = new Date();
      const cooldownTime = new Date(now.getTime() - this.TOKEN_COOLDOWN_HOURS * 60 * 60 * 1000);

      let query = supabaseAdmin
        .from('token_pool')
        .select('*')
        .eq('is_healthy', true)
        .lt('usage_count', this.MAX_DAILY_USAGE_PER_TOKEN)
        .or(`last_used_at.is.null,last_used_at.lt.${cooldownTime.toISOString()}`)
        // 新增：只选择未分配或分配已过期的Token
        .or(`allocated_to.is.null,allocation_expires_at.lt.${now.toISOString()}`)
        .order('created_at', { ascending: true })  // 优先使用创建时间早的Token
        .order('usage_count', { ascending: true })
        .limit(50);  // 增加限制数量

      // 排除指定的Token
      if (excludeTokenIds.length > 0) {
        query = query.not('id', 'in', `(${excludeTokenIds.map(id => `'${id}'`).join(',')})`);
      }

      const { data: tokens } = await query;
      return tokens || [];
    } catch (error) {
      console.error('Failed to get available tokens:', error);
      return [];
    }
  }

  /**
   * 获取备用Token（当没有理想Token时使用）
   */
  private static async getFallbackTokens(): Promise<TokenPool[]> {
    if (!supabaseAdmin) return [];

    try {
      const { data: tokens } = await supabaseAdmin
        .from('token_pool')
        .select('*')
        .eq('is_healthy', true)
        .order('usage_count', { ascending: true })
        .limit(5);

      return tokens || [];
    } catch (error) {
      console.error('Failed to get fallback tokens:', error);
      return [];
    }
  }

  /**
   * 智能选择最优Token
   * 基于多个因素的加权算法
   */
  private static selectOptimalToken(tokens: TokenPool[]): TokenPool {
    if (tokens.length === 1) {
      return tokens[0];
    }

    // 计算每个Token的得分
    const scoredTokens = tokens.map(token => {
      let score = 0;

      // 1. 使用频率得分（使用越少得分越高）
      const maxUsage = Math.max(...tokens.map(t => t.usage_count));
      const usageScore = maxUsage > 0 ? (maxUsage - token.usage_count) / maxUsage : 1;
      score += usageScore * 40; // 40%权重

      // 2. 时间得分（最后使用时间越久得分越高）
      const now = Date.now();
      const lastUsed = token.last_used_at ? new Date(token.last_used_at).getTime() : 0;
      const timeDiff = now - lastUsed;
      const timeScore = Math.min(timeDiff / (24 * 60 * 60 * 1000), 1); // 最多1天
      score += timeScore * 30; // 30%权重

      // 3. 错误计数得分（错误越少得分越高）
      const errorScore = Math.max(0, 1 - (token.error_count || 0) / 10);
      score += errorScore * 20; // 20%权重

      // 4. 随机因子（避免总是选择同一个Token）
      const randomScore = Math.random();
      score += randomScore * 10; // 10%权重

      return { token, score };
    });

    // 选择得分最高的Token
    scoredTokens.sort((a, b) => b.score - a.score);
    return scoredTokens[0].token;
  }

  /**
   * 用户点击"一键更新Token"时，将当前分配给用户的Token标记为异常
   * 逻辑：用户主动要求更新Token，说明当前Token很可能有问题
   */
  static async markUserTokensAsUnhealthy(userId: string): Promise<void> {
    if (!supabaseAdmin) return;

    try {
      const now = new Date();

      // 查找用户当前分配的所有有效Token
      const { data: userTokens } = await supabaseAdmin
        .from('token_pool')
        .select('id, access_token')
        .eq('allocated_to', userId)
        .eq('is_healthy', true)
        .gt('allocation_expires_at', now.toISOString());

      if (!userTokens || userTokens.length === 0) {
        console.log(`No active tokens found for user ${userId}`);
        return;
      }

      // 将所有分配给用户的Token标记为不健康
      const { error } = await supabaseAdmin
        .from('token_pool')
        .update({
          is_healthy: false,
          notes: 'Marked as unhealthy due to user token update request',
          updated_at: now.toISOString()
        })
        .eq('allocated_to', userId)
        .eq('is_healthy', true)
        .gt('allocation_expires_at', now.toISOString());

      if (error) {
        console.error('Failed to mark user tokens as unhealthy:', error);
        return;
      }

      console.log(`✅ Marked ${userTokens.length} tokens as unhealthy for user ${userId}`);

      // 记录日志
      for (const token of userTokens) {
        console.log(`  - Token ${token.id.substring(0, 8)}... marked as unhealthy`);
      }

    } catch (error) {
      console.error('Failed to mark user tokens as unhealthy:', error);
    }
  }

  /**
   * 分配Token给用户（24小时有效期）
   */
  static async allocateTokenToUser(userId: string): Promise<{ success: boolean; token?: TokenInfo; error?: string }> {
    if (!supabaseAdmin) return { success: false, error: 'Database not available' };

    try {
      // 1. 🔥 关键逻辑：用户请求新Token时，将其当前Token标记为异常
      //    因为用户主动要求更新Token，说明当前Token很可能有问题
      await this.markUserTokensAsUnhealthy(userId);

      // 2. 获取可用Token
      const availableTokens = await this.getAvailableTokens([]);

      if (availableTokens.length === 0) {
        return { success: false, error: 'No available tokens' };
      }

      // 2. 选择最优Token（创建时间最早的）
      const selectedToken = availableTokens[0];

      // 3. 设置分配过期时间（24小时后）
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000);

      // 4. 原子性地分配Token
      const { error: updateError } = await supabaseAdmin
        .from('token_pool')
        .update({
          allocated_to: userId,
          allocated_at: now.toISOString(),
          allocation_expires_at: expiresAt.toISOString()
        })
        .eq('id', selectedToken.id)
        .is('allocated_to', null); // 确保Token未被其他用户分配

      if (updateError) {
        console.error('Failed to allocate token:', updateError);
        return { success: false, error: 'Failed to allocate token' };
      }

      // 5. 转换为插件兼容格式
      const tokenInfo: TokenInfo = {
        id: selectedToken.id,
        accessToken: selectedToken.access_token,
        tenantURL: selectedToken.tenant_url,
        use_time: selectedToken.usage_count,
        created_at: selectedToken.created_at,
        updated_at: selectedToken.updated_at
      };

      console.log(`Token ${selectedToken.id} allocated to user ${userId}, expires at ${expiresAt.toISOString()}`);

      return { success: true, token: tokenInfo };

    } catch (error) {
      console.error('Failed to allocate token:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * 获取用户当前分配的Token列表
   */
  static async getUserAllocatedTokens(userId: string): Promise<TokenInfo[]> {
    if (!supabaseAdmin) return [];

    try {
      const now = new Date();

      const { data: tokens } = await supabaseAdmin
        .from('token_pool')
        .select('*')
        .eq('allocated_to', userId)
        .eq('is_healthy', true)
        .gt('allocation_expires_at', now.toISOString())  // 只返回未过期的分配
        .order('allocated_at', { ascending: false });

      if (!tokens) return [];

      // 转换为插件兼容格式
      return tokens.map(token => ({
        id: token.id,
        accessToken: token.access_token,
        tenantURL: token.tenant_url,
        use_time: token.usage_count,
        created_at: token.created_at,
        updated_at: token.updated_at
      }));

    } catch (error) {
      console.error('Failed to get user allocated tokens:', error);
      return [];
    }
  }

  /**
   * 释放过期的Token分配
   */
  static async releaseExpiredAllocations(): Promise<void> {
    if (!supabaseAdmin) return;

    try {
      const now = new Date();

      const { error } = await supabaseAdmin
        .from('token_pool')
        .update({
          allocated_to: null,
          allocated_at: null,
          allocation_expires_at: null
        })
        .lt('allocation_expires_at', now.toISOString())
        .not('allocated_to', 'is', null);

      if (error) {
        console.error('Failed to release expired allocations:', error);
      } else {
        console.log('Released expired token allocations');
      }

    } catch (error) {
      console.error('Failed to release expired allocations:', error);
    }
  }

  /**
   * 获取Token分配统计信息
   */
  static async getAllocationStats(): Promise<{
    totalTokens: number;
    healthyTokens: number;
    allocatedTokens: number;
    expiredAllocations: number;
    availableTokens: number;
  }> {
    if (!supabaseAdmin) {
      return {
        totalTokens: 0,
        healthyTokens: 0,
        allocatedTokens: 0,
        expiredAllocations: 0,
        availableTokens: 0
      };
    }

    try {
      const now = new Date();

      // 总Token数
      const { count: totalTokens } = await supabaseAdmin
        .from('token_pool')
        .select('*', { count: 'exact', head: true });

      // 健康Token数
      const { count: healthyTokens } = await supabaseAdmin
        .from('token_pool')
        .select('*', { count: 'exact', head: true })
        .eq('is_healthy', true);

      // 当前分配的Token数
      const { count: allocatedTokens } = await supabaseAdmin
        .from('token_pool')
        .select('*', { count: 'exact', head: true })
        .not('allocated_to', 'is', null)
        .gt('allocation_expires_at', now.toISOString());

      // 过期分配数
      const { count: expiredAllocations } = await supabaseAdmin
        .from('token_pool')
        .select('*', { count: 'exact', head: true })
        .not('allocated_to', 'is', null)
        .lt('allocation_expires_at', now.toISOString());

      // 可用Token数（健康且未分配或分配已过期）
      const { count: availableTokens } = await supabaseAdmin
        .from('token_pool')
        .select('*', { count: 'exact', head: true })
        .eq('is_healthy', true)
        .or(`allocated_to.is.null,allocation_expires_at.lt.${now.toISOString()}`);

      return {
        totalTokens: totalTokens || 0,
        healthyTokens: healthyTokens || 0,
        allocatedTokens: allocatedTokens || 0,
        expiredAllocations: expiredAllocations || 0,
        availableTokens: availableTokens || 0
      };

    } catch (error) {
      console.error('Failed to get allocation stats:', error);
      return {
        totalTokens: 0,
        healthyTokens: 0,
        allocatedTokens: 0,
        expiredAllocations: 0,
        availableTokens: 0
      };
    }
  }

  /**
   * 更新Token使用统计
   */
  private static async updateTokenUsageStats(tokenId: string, userId: string): Promise<void> {
    if (!supabaseAdmin) return;

    try {
      // 使用数据库函数原子性地更新使用计数
      await supabaseAdmin.rpc('increment_usage_count', { token_id: tokenId });

      // 记录使用记录
      await supabaseAdmin
        .from('token_usage')
        .insert({
          user_id: userId,
          token_id: tokenId,
          used_at: new Date().toISOString(),
          request_count: 1,
          success: true
        });

    } catch (error) {
      console.error('Failed to update token usage stats:', error);
    }
  }

  /**
   * 批量获取健康Token（兼容插件API）
   */
  static async getHealthyTokensBatch(
    page: number = 1, 
    limit: number = 50,
    userId?: string
  ): Promise<TokenInfo[]> {
    if (!supabaseAdmin) return [];

    try {
      const offset = (page - 1) * limit;
      
      // 获取用户最近使用的Token（如果提供了userId）
      let excludeTokenIds: string[] = [];
      if (userId) {
        excludeTokenIds = await this.getUserRecentTokens(userId, 3);
      }

      const now = new Date();

      let query = supabaseAdmin
        .from('token_pool')
        .select('*')
        .eq('is_healthy', true)
        // 只返回未分配或分配已过期的Token
        .or(`allocated_to.is.null,allocation_expires_at.lt.${now.toISOString()}`)
        .order('created_at', { ascending: true })  // 按创建时间升序排序
        .order('usage_count', { ascending: true })
        .range(offset, offset + limit - 1);

      // 排除指定的Token（如果有的话）
      if (excludeTokenIds.length > 0) {
        query = query.not('id', 'in', `(${excludeTokenIds.map(id => `'${id}'`).join(',')})`);
      }

      const { data: tokens } = await query;

      if (!tokens) return [];

      // 转换为插件兼容格式
      return tokens.map(token => ({
        id: token.id,
        accessToken: token.access_token,
        tenantURL: token.tenant_url,  // 使用驼峰命名兼容VSCode扩展
        use_time: token.usage_count,
        created_at: token.created_at,
        updated_at: token.updated_at
      }));

    } catch (error) {
      console.error('SmartTokenManager.getHealthyTokensBatch error:', error);
      return [];
    }
  }

  /**
   * 添加新Token到池中
   */
  static async addTokenToPool(
    accessToken: string,
    tenantUrl: string = 'https://d5.api.augmentcode.com/',
    source: 'oauth' | 'manual' | 'auto' = 'manual',
    refreshToken?: string
  ): Promise<{ success: boolean; tokenId?: string; error?: string }> {
    if (!supabaseAdmin) {
      return { success: false, error: 'Database not configured' };
    }

    try {
      // 检查Token是否已存在
      const { data: existing } = await supabaseAdmin
        .from('token_pool')
        .select('id')
        .eq('access_token', accessToken)
        .single();

      if (existing) {
        return { success: false, error: 'Token already exists in pool' };
      }

      // 验证Token有效性
      const healthStatus = await TokenHealthMonitor.checkTokenHealth({
        id: 'temp',
        access_token: accessToken,
        tenant_url: tenantUrl,
        is_healthy: true,
        usage_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        source,
        error_count: 0,
        max_daily_usage: 1000
      } as TokenPool);

      if (!healthStatus.is_healthy) {
        return { 
          success: false, 
          error: `Token无效: ${healthStatus.error_message}` 
        };
      }

      // 添加到Token池
      const { data: newToken, error } = await supabaseAdmin
        .from('token_pool')
        .insert({
          access_token: accessToken,
          tenant_url: tenantUrl,
          refresh_token: refreshToken,
          source,
          is_healthy: true,
          usage_count: 0,
          max_daily_usage: 1000
        })
        .select('id')
        .single();

      if (error) {
        return { success: false, error: `添加Token失败: ${error.message}` };
      }

      return { 
        success: true, 
        tokenId: newToken.id 
      };

    } catch (error) {
      console.error('SmartTokenManager.addTokenToPool error:', error);
      return { success: false, error: '添加Token时发生异常' };
    }
  }

  /**
   * 自动维护Token池
   * 定期清理不健康Token，补充新Token
   */
  static async maintainTokenPool(): Promise<{
    cleaned: number;
    healthyCount: number;
    needsAttention: boolean;
  }> {
    if (!supabaseAdmin) {
      return { cleaned: 0, healthyCount: 0, needsAttention: true };
    }

    try {
      // 1. 清理长期不健康的Token
      const cleaned = await TokenHealthMonitor.cleanupUnhealthyTokens(5);

      // 2. 获取当前健康Token数量
      const { data: healthyTokens } = await supabaseAdmin
        .from('token_pool')
        .select('id')
        .eq('is_healthy', true);

      const healthyCount = healthyTokens?.length || 0;

      // 3. 判断是否需要关注
      const needsAttention = healthyCount < this.MIN_HEALTHY_TOKENS;

      if (needsAttention) {
        console.warn(`Token pool needs attention: only ${healthyCount} healthy tokens remaining`);
        // 这里可以发送警告通知或触发自动补充机制
      }

      return {
        cleaned,
        healthyCount,
        needsAttention
      };

    } catch (error) {
      console.error('SmartTokenManager.maintainTokenPool error:', error);
      return { cleaned: 0, healthyCount: 0, needsAttention: true };
    }
  }

  /**
   * 获取Token使用统计
   */
  static async getTokenUsageStats(tokenId: string): Promise<{
    totalUsage: number;
    dailyUsage: number;
    lastUsed: string | null;
    averageResponseTime: number;
  }> {
    if (!supabaseAdmin) {
      return { totalUsage: 0, dailyUsage: 0, lastUsed: null, averageResponseTime: 0 };
    }

    try {
      // 获取Token基本信息
      const { data: token } = await supabaseAdmin
        .from('token_pool')
        .select('usage_count, last_used_at')
        .eq('id', tokenId)
        .single();

      if (!token) {
        return { totalUsage: 0, dailyUsage: 0, lastUsed: null, averageResponseTime: 0 };
      }

      // 获取今日使用量
      const { data: dailyUsage } = await supabaseAdmin.rpc('get_token_daily_usage', {
        token_id: tokenId
      });

      // 获取平均响应时间
      const { data: responseTimes } = await supabaseAdmin
        .from('token_health_logs')
        .select('response_time')
        .eq('token_id', tokenId)
        .not('response_time', 'is', null)
        .gte('checked_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
        .order('checked_at', { ascending: false })
        .limit(10);

      const averageResponseTime = responseTimes && responseTimes.length > 0
        ? responseTimes.reduce((sum, log) => sum + (log.response_time || 0), 0) / responseTimes.length
        : 0;

      return {
        totalUsage: token.usage_count || 0,
        dailyUsage: dailyUsage || 0,
        lastUsed: token.last_used_at,
        averageResponseTime: Math.round(averageResponseTime)
      };

    } catch (error) {
      console.error('SmartTokenManager.getTokenUsageStats error:', error);
      return { totalUsage: 0, dailyUsage: 0, lastUsed: null, averageResponseTime: 0 };
    }
  }

  /**
   * 预测Token剩余可用时间
   */
  static async predictTokenLifetime(tokenId: string): Promise<{
    estimatedHoursRemaining: number;
    confidenceLevel: 'high' | 'medium' | 'low';
    recommendation: string;
  }> {
    try {
      const stats = await this.getTokenUsageStats(tokenId);
      
      if (stats.dailyUsage === 0) {
        return {
          estimatedHoursRemaining: 24,
          confidenceLevel: 'low',
          recommendation: 'Token未使用，无法预测'
        };
      }

      // 基于当前使用率预测
      const hourlyUsage = stats.dailyUsage / 24;
      const remainingCapacity = this.MAX_DAILY_USAGE_PER_TOKEN - stats.dailyUsage;
      const estimatedHours = hourlyUsage > 0 ? remainingCapacity / hourlyUsage : 24;

      let confidenceLevel: 'high' | 'medium' | 'low' = 'medium';
      let recommendation = '正常使用';

      if (estimatedHours < 2) {
        confidenceLevel = 'high';
        recommendation = '建议立即更换Token';
      } else if (estimatedHours < 6) {
        confidenceLevel = 'high';
        recommendation = '建议准备备用Token';
      } else if (estimatedHours > 20) {
        confidenceLevel = 'low';
        recommendation = 'Token状态良好';
      }

      return {
        estimatedHoursRemaining: Math.round(estimatedHours),
        confidenceLevel,
        recommendation
      };

    } catch (error) {
      console.error('SmartTokenManager.predictTokenLifetime error:', error);
      return {
        estimatedHoursRemaining: 0,
        confidenceLevel: 'low',
        recommendation: '预测失败'
      };
    }
  }

  /**
   * 启动自动维护任务
   */
  static startAutoMaintenance(): void {
    // 每30分钟检查一次Token池状态
    setInterval(async () => {
      try {
        await this.maintainTokenPool();
      } catch (error) {
        console.error('Auto maintenance failed:', error);
      }
    }, 30 * 60 * 1000);

    // 每5分钟进行一次健康检查
    TokenHealthMonitor.startPeriodicHealthCheck(5);

    console.log('Auto maintenance started');
  }
}
