/**
 * Supabase客户端配置
 * 支持服务端和客户端使用
 */

import { createClient } from '@supabase/supabase-js';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import type { Database } from '@/types/database';

// 环境变量验证
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

/**
 * 客户端Supabase实例
 * 用于浏览器端的数据操作
 */
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
});

/**
 * 服务端Supabase实例
 * 用于API路由和服务端渲染
 */
export async function createServerSupabaseClient() {
  const cookieStore = await cookies();

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase configuration');
  }

  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll();
      },
      setAll(cookiesToSet: Array<{name: string; value: string; options?: any}>) {
        try {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookieStore.set(name, value, options);
          });
        } catch (error) {
          // 在某些情况下设置cookie可能失败，这里静默处理
          console.warn('Failed to set cookies:', error);
        }
      },
    },
  });
}

/**
 * 管理员Supabase实例
 * 使用service role key，拥有完全权限
 */
export const supabaseAdmin = supabaseServiceKey 
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    })
  : null;

/**
 * 数据库表名常量
 */
export const TABLES = {
  USERS: 'users',
  TOKEN_POOL: 'token_pool',
  TOKEN_USAGE: 'token_usage',
  SYSTEM_CONFIG: 'system_config',
} as const;

/**
 * 通用数据库操作工具
 */
export class DatabaseUtils {
  /**
   * 安全地执行数据库查询，包含错误处理
   */
  static async safeQuery<T>(
    queryFn: () => Promise<{ data: T | null; error: any }>
  ): Promise<{ data: T | null; error: string | null }> {
    try {
      const result = await queryFn();
      
      if (result.error) {
        console.error('Database query error:', result.error);
        return { data: null, error: result.error.message || 'Database query failed' };
      }
      
      return { data: result.data, error: null };
    } catch (error) {
      console.error('Unexpected database error:', error);
      return { 
        data: null, 
        error: error instanceof Error ? error.message : 'Unknown database error' 
      };
    }
  }

  /**
   * 生成UUID
   */
  static generateUUID(): string {
    return crypto.randomUUID();
  }

  /**
   * 验证UUID格式
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * 安全地解析JSON
   */
  static safeJsonParse<T>(jsonString: string): T | null {
    try {
      return JSON.parse(jsonString);
    } catch {
      return null;
    }
  }
}

/**
 * 错误处理工具
 */
export class ErrorHandler {
  static handleDatabaseError(error: any): string {
    if (error?.code === '23505') {
      return '数据已存在，请检查输入';
    }
    if (error?.code === '23503') {
      return '关联数据不存在';
    }
    if (error?.code === 'PGRST116') {
      return '数据不存在';
    }
    
    return error?.message || '数据库操作失败';
  }

  static handleApiError(error: any): Response {
    const message = error instanceof Error ? error.message : '未知错误';
    
    return Response.json(
      { error: message },
      { status: error.status || 500 }
    );
  }
}

export default supabase;
