/**
 * 系统初始化服务
 * 启动时自动执行的初始化任务
 */

import { supabaseAdmin } from './supabase';
import { TokenHealthMonitor } from './token-health-monitor';
import { SmartTokenManager } from './smart-token-manager';
import { OAuthService } from './oauth-service';

export class SystemInitializer {
  private static isInitialized = false;

  /**
   * 系统启动初始化
   */
  static async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log('🚀 Initializing Augment Token Service...');

    try {
      // 1. 验证数据库连接
      await this.verifyDatabaseConnection();

      // 2. 初始化系统配置
      await this.initializeSystemConfig();

      // 3. 启动定时任务
      this.startScheduledTasks();

      // 4. 执行初始健康检查
      await this.performInitialHealthCheck();

      this.isInitialized = true;
      console.log('✅ System initialization completed successfully');

    } catch (error) {
      console.error('❌ System initialization failed:', error);
      throw error;
    }
  }

  /**
   * 验证数据库连接
   */
  private static async verifyDatabaseConnection(): Promise<void> {
    if (!supabaseAdmin) {
      throw new Error('Supabase admin client not configured');
    }

    try {
      // 测试数据库连接
      const { data, error } = await supabaseAdmin
        .from('system_config')
        .select('key')
        .limit(1);

      if (error) {
        throw new Error(`Database connection failed: ${error.message}`);
      }

      console.log('✅ Database connection verified');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  /**
   * 初始化系统配置
   */
  private static async initializeSystemConfig(): Promise<void> {
    if (!supabaseAdmin) return;

    try {
      const defaultConfigs = [
        { key: 'token_pool_min_size', value: 50, description: 'Token池最小数量' },
        { key: 'token_pool_max_size', value: 200, description: 'Token池最大数量' },
        { key: 'health_check_interval', value: 300, description: 'Token健康检查间隔（秒）' },
        { key: 'max_requests_per_token_per_day', value: 1000, description: '每个Token每日最大请求数' },
        { key: 'free_user_daily_limit', value: 10, description: '免费用户每日请求限制' },
        { key: 'pro_user_daily_limit', value: 1000, description: '专业用户每日请求限制' },
        { key: 'enterprise_user_daily_limit', value: -1, description: '企业用户每日请求限制' },
        { key: 'api_version', value: '1.0.0', description: 'API版本号' },
        { key: 'maintenance_mode', value: false, description: '维护模式开关' },
        { key: 'auto_cleanup_enabled', value: true, description: '自动清理功能开关' }
      ];

      for (const config of defaultConfigs) {
        // 检查配置是否已存在
        const { data: existing } = await supabaseAdmin
          .from('system_config')
          .select('key')
          .eq('key', config.key)
          .single();

        if (!existing) {
          // 插入新配置
          await supabaseAdmin
            .from('system_config')
            .insert({
              key: config.key,
              value: config.value,
              description: config.description
            });
        }
      }

      console.log('✅ System configuration initialized');
    } catch (error) {
      console.error('❌ Failed to initialize system config:', error);
      throw error;
    }
  }

  /**
   * 启动定时任务
   */
  private static startScheduledTasks(): void {
    try {
      // 启动Token健康监控（每5分钟）
      TokenHealthMonitor.startPeriodicHealthCheck(5);

      // 启动Token刷新调度器（每小时）
      OAuthService.startTokenRefreshScheduler();

      // 启动自动维护（每30分钟）
      SmartTokenManager.startAutoMaintenance();

      // 启动数据清理任务（每天凌晨2点）
      this.startDailyCleanupTask();

      console.log('✅ Scheduled tasks started');
    } catch (error) {
      console.error('❌ Failed to start scheduled tasks:', error);
      throw error;
    }
  }

  /**
   * 执行初始健康检查
   */
  private static async performInitialHealthCheck(): Promise<void> {
    try {
      console.log('🔍 Performing initial health check...');
      
      // 获取Token池统计
      const stats = await TokenHealthMonitor.getTokenPoolStats();
      
      console.log(`📊 Token pool status: ${stats.healthy}/${stats.total} healthy tokens`);

      // 如果健康Token数量过低，发出警告
      if (stats.healthy < 20) {
        console.warn(`⚠️  Warning: Only ${stats.healthy} healthy tokens available!`);
      }

      // 执行Token池维护
      const maintenanceResult = await SmartTokenManager.maintainTokenPool();
      
      if (maintenanceResult.needsAttention) {
        console.warn('⚠️  Token pool needs attention - consider adding more tokens');
      }

      console.log('✅ Initial health check completed');
    } catch (error) {
      console.error('❌ Initial health check failed:', error);
      // 不抛出错误，因为这不是关键操作
    }
  }

  /**
   * 启动每日数据清理任务
   */
  private static startDailyCleanupTask(): void {
    const now = new Date();
    const tomorrow2AM = new Date(now);
    tomorrow2AM.setDate(tomorrow2AM.getDate() + 1);
    tomorrow2AM.setHours(2, 0, 0, 0);

    const msUntil2AM = tomorrow2AM.getTime() - now.getTime();

    // 设置首次执行时间为明天凌晨2点
    setTimeout(() => {
      this.performDailyCleanup();
      
      // 然后每24小时执行一次
      setInterval(() => {
        this.performDailyCleanup();
      }, 24 * 60 * 60 * 1000);
      
    }, msUntil2AM);

    console.log('✅ Daily cleanup task scheduled');
  }

  /**
   * 执行每日数据清理
   */
  private static async performDailyCleanup(): Promise<void> {
    if (!supabaseAdmin) return;

    try {
      console.log('🧹 Starting daily cleanup...');

      // 清理旧的使用记录（保留30天）
      const { data: cleanedUsage } = await supabaseAdmin.rpc('cleanup_old_usage_records');
      
      // 清理旧的健康检查日志（保留7天）
      const { data: cleanedLogs } = await supabaseAdmin.rpc('cleanup_old_health_logs');

      // 清理长期不健康的Token
      const cleanedTokens = await TokenHealthMonitor.cleanupUnhealthyTokens(10);

      console.log(`🧹 Daily cleanup completed:`, {
        cleanedUsageRecords: cleanedUsage || 0,
        cleanedHealthLogs: cleanedLogs || 0,
        cleanedTokens: cleanedTokens
      });

    } catch (error) {
      console.error('❌ Daily cleanup failed:', error);
    }
  }

  /**
   * 获取系统状态
   */
  static async getSystemStatus(): Promise<{
    isHealthy: boolean;
    services: {
      database: boolean;
      tokenPool: boolean;
      monitoring: boolean;
    };
    stats: {
      totalTokens: number;
      healthyTokens: number;
      averageResponseTime: number;
    };
  }> {
    try {
      // 检查数据库状态
      const databaseHealthy = await this.checkDatabaseHealth();
      
      // 检查Token池状态
      const tokenPoolStats = await TokenHealthMonitor.getTokenPoolStats();
      const tokenPoolHealthy = tokenPoolStats.healthy > 10; // 至少10个健康Token

      // 检查监控系统状态
      const monitoringHealthy = this.isInitialized;

      const isHealthy = databaseHealthy && tokenPoolHealthy && monitoringHealthy;

      return {
        isHealthy,
        services: {
          database: databaseHealthy,
          tokenPool: tokenPoolHealthy,
          monitoring: monitoringHealthy
        },
        stats: {
          totalTokens: tokenPoolStats.total,
          healthyTokens: tokenPoolStats.healthy,
          averageResponseTime: tokenPoolStats.averageResponseTime
        }
      };

    } catch (error) {
      console.error('Failed to get system status:', error);
      return {
        isHealthy: false,
        services: {
          database: false,
          tokenPool: false,
          monitoring: false
        },
        stats: {
          totalTokens: 0,
          healthyTokens: 0,
          averageResponseTime: 0
        }
      };
    }
  }

  /**
   * 检查数据库健康状态
   */
  private static async checkDatabaseHealth(): Promise<boolean> {
    if (!supabaseAdmin) return false;

    try {
      const { error } = await supabaseAdmin
        .from('system_config')
        .select('key')
        .limit(1);

      return !error;
    } catch {
      return false;
    }
  }

  /**
   * 优雅关闭系统
   */
  static async shutdown(): Promise<void> {
    console.log('🛑 Shutting down system...');
    
    try {
      // 这里可以添加清理逻辑
      // 例如：保存状态、关闭连接等
      
      this.isInitialized = false;
      console.log('✅ System shutdown completed');
    } catch (error) {
      console.error('❌ System shutdown failed:', error);
    }
  }
}

// 在模块加载时自动初始化
if (typeof window === 'undefined') {
  // 只在服务端执行初始化
  SystemInitializer.initialize().catch(error => {
    console.error('Failed to initialize system:', error);
  });
}

export default SystemInitializer;
