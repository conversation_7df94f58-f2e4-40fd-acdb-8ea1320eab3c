/**
 * Token健康监控系统
 * 实现智能的Token健康检查和自动恢复机制
 */

import { supabaseAdmin } from './supabase';
import type { TokenPool, TokenHealthStatus } from '@/types';

export class TokenHealthMonitor {
  private static readonly HEALTH_CHECK_TIMEOUT = 10000; // 10秒超时
  private static readonly MAX_CONCURRENT_CHECKS = 10; // 最大并发检查数
  private static readonly AUGMENT_TEST_ENDPOINT = 'https://d5.api.augmentcode.com/v1/chat/completions';

  /**
   * 检查单个Token的健康状态
   */
  static async checkTokenHealth(token: TokenPool): Promise<TokenHealthStatus> {
    const startTime = Date.now();
    
    try {
      // 发送测试请求到Augment API
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.HEALTH_CHECK_TIMEOUT);

      const response = await fetch(this.AUGMENT_TEST_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token.access_token}`,
          'Content-Type': 'application/json',
          'User-Agent': 'AugmentTokenService/1.0.0'
        },
        body: JSON.stringify({
          model: 'claude-3-5-sonnet-20241022',
          messages: [{ role: 'user', content: 'test' }],
          max_tokens: 1,
          stream: false
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      // 判断Token健康状态
      const isHealthy = response.status !== 401 && 
                       response.status !== 403 && 
                       response.status !== 429 &&
                       response.status < 500;

      let errorMessage: string | undefined;
      if (!isHealthy) {
        try {
          const errorData = await response.json();
          errorMessage = errorData.error?.message || `HTTP ${response.status}`;
        } catch {
          errorMessage = `HTTP ${response.status}`;
        }
      }

      return {
        token_id: token.id,
        is_healthy: isHealthy,
        last_checked: new Date().toISOString(),
        response_time: responseTime,
        error_message: errorMessage
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Request timeout';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        token_id: token.id,
        is_healthy: false,
        last_checked: new Date().toISOString(),
        response_time: responseTime,
        error_message: errorMessage
      };
    }
  }

  /**
   * 批量检查所有Token的健康状态
   */
  static async checkAllTokensHealth(): Promise<TokenHealthStatus[]> {
    if (!supabaseAdmin) {
      throw new Error('Database not configured');
    }

    try {
      // 获取所有Token
      const { data: tokens, error } = await supabaseAdmin
        .from('token_pool')
        .select('*')
        .order('last_used_at', { ascending: true, nullsFirst: true });

      if (error) {
        throw new Error(`Failed to fetch tokens: ${error.message}`);
      }

      if (!tokens || tokens.length === 0) {
        return [];
      }

      // 分批检查Token健康状态
      const results: TokenHealthStatus[] = [];
      const batches = this.chunkArray(tokens, this.MAX_CONCURRENT_CHECKS);

      for (const batch of batches) {
        const batchPromises = batch.map(token => this.checkTokenHealth(token));
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // 批次间短暂延迟，避免过于频繁的请求
        if (batches.indexOf(batch) < batches.length - 1) {
          await this.delay(1000);
        }
      }

      // 批量更新数据库
      await this.updateTokenHealthInDatabase(results);

      return results;
    } catch (error) {
      console.error('TokenHealthMonitor.checkAllTokensHealth error:', error);
      throw error;
    }
  }

  /**
   * 更新数据库中的Token健康状态
   */
  private static async updateTokenHealthInDatabase(healthStatuses: TokenHealthStatus[]): Promise<void> {
    if (!supabaseAdmin) return;

    try {
      // 准备批量更新数据
      const healthData = healthStatuses.map(status => ({
        id: status.token_id,
        is_healthy: status.is_healthy,
        response_time: status.response_time,
        error_message: status.error_message
      }));

      // 使用数据库函数进行批量更新
      const { error } = await supabaseAdmin.rpc('batch_update_token_health', {
        token_health_data: healthData
      });

      if (error) {
        console.error('Failed to update token health in database:', error);
      }
    } catch (error) {
      console.error('TokenHealthMonitor.updateTokenHealthInDatabase error:', error);
    }
  }

  /**
   * 获取Token池统计信息
   */
  static async getTokenPoolStats(): Promise<{
    total: number;
    healthy: number;
    unhealthy: number;
    averageResponseTime: number;
  }> {
    if (!supabaseAdmin) {
      return { total: 0, healthy: 0, unhealthy: 0, averageResponseTime: 0 };
    }

    try {
      const { data: stats } = await supabaseAdmin.rpc('get_token_pool_stats');
      
      if (!stats || stats.length === 0) {
        return { total: 0, healthy: 0, unhealthy: 0, averageResponseTime: 0 };
      }

      const stat = stats[0];
      
      // 获取平均响应时间
      const { data: avgResponseTime } = await supabaseAdmin
        .from('token_health_logs')
        .select('response_time')
        .not('response_time', 'is', null)
        .gte('checked_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // 最近24小时
        .order('checked_at', { ascending: false })
        .limit(100);

      const averageResponseTime = avgResponseTime && avgResponseTime.length > 0
        ? avgResponseTime.reduce((sum, log) => sum + (log.response_time || 0), 0) / avgResponseTime.length
        : 0;

      return {
        total: stat.total_tokens || 0,
        healthy: stat.healthy_tokens || 0,
        unhealthy: stat.unhealthy_tokens || 0,
        averageResponseTime: Math.round(averageResponseTime)
      };
    } catch (error) {
      console.error('TokenHealthMonitor.getTokenPoolStats error:', error);
      return { total: 0, healthy: 0, unhealthy: 0, averageResponseTime: 0 };
    }
  }

  /**
   * 自动清理不健康的Token
   */
  static async cleanupUnhealthyTokens(maxErrorCount: number = 10): Promise<number> {
    if (!supabaseAdmin) return 0;

    try {
      const { data: deletedTokens, error } = await supabaseAdmin
        .from('token_pool')
        .delete()
        .eq('is_healthy', false)
        .gte('error_count', maxErrorCount)
        .select('id');

      if (error) {
        console.error('Failed to cleanup unhealthy tokens:', error);
        return 0;
      }

      return deletedTokens?.length || 0;
    } catch (error) {
      console.error('TokenHealthMonitor.cleanupUnhealthyTokens error:', error);
      return 0;
    }
  }

  /**
   * 工具方法：将数组分块
   */
  private static chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 工具方法：延迟执行
   */
  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 启动定时健康检查
   */
  static startPeriodicHealthCheck(intervalMinutes: number = 5): void {
    const intervalMs = intervalMinutes * 60 * 1000;
    
    setInterval(async () => {
      try {
        console.log('Starting periodic token health check...');
        const results = await this.checkAllTokensHealth();
        const healthyCount = results.filter(r => r.is_healthy).length;
        console.log(`Health check completed: ${healthyCount}/${results.length} tokens healthy`);
        
        // 如果健康Token数量过低，触发警告
        if (healthyCount < results.length * 0.3) {
          console.warn(`Warning: Only ${healthyCount} healthy tokens remaining!`);
          // 这里可以发送警告通知
        }
      } catch (error) {
        console.error('Periodic health check failed:', error);
      }
    }, intervalMs);
  }
}
