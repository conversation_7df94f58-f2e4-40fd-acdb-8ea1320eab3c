/**
 * Supabase数据库类型定义
 * 自动生成的类型，确保类型安全
 */

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          user_token: string;
          subscription_type: 'free' | 'pro' | 'enterprise';
          created_at: string;
          updated_at: string;
          is_active: boolean;
        };
        Insert: {
          id?: string;
          email: string;
          user_token: string;
          subscription_type?: 'free' | 'pro' | 'enterprise';
          created_at?: string;
          updated_at?: string;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          email?: string;
          user_token?: string;
          subscription_type?: 'free' | 'pro' | 'enterprise';
          created_at?: string;
          updated_at?: string;
          is_active?: boolean;
        };
      };
      token_pool: {
        Row: {
          id: string;
          access_token: string;
          tenant_url: string;
          refresh_token: string | null;
          expires_at: string | null;
          created_at: string;
          updated_at: string;
          is_healthy: boolean;
          last_used_at: string | null;
          usage_count: number;
          source: 'oauth' | 'manual' | 'auto';
        };
        Insert: {
          id?: string;
          access_token: string;
          tenant_url: string;
          refresh_token?: string | null;
          expires_at?: string | null;
          created_at?: string;
          updated_at?: string;
          is_healthy?: boolean;
          last_used_at?: string | null;
          usage_count?: number;
          source?: 'oauth' | 'manual' | 'auto';
        };
        Update: {
          id?: string;
          access_token?: string;
          tenant_url?: string;
          refresh_token?: string | null;
          expires_at?: string | null;
          created_at?: string;
          updated_at?: string;
          is_healthy?: boolean;
          last_used_at?: string | null;
          usage_count?: number;
          source?: 'oauth' | 'manual' | 'auto';
        };
      };
      token_usage: {
        Row: {
          id: string;
          user_id: string;
          token_id: string;
          used_at: string;
          request_count: number;
          session_id: string | null;
          user_agent: string | null;
          ip_address: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          token_id: string;
          used_at?: string;
          request_count?: number;
          session_id?: string | null;
          user_agent?: string | null;
          ip_address?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          token_id?: string;
          used_at?: string;
          request_count?: number;
          session_id?: string | null;
          user_agent?: string | null;
          ip_address?: string | null;
        };
      };
      system_config: {
        Row: {
          key: string;
          value: any;
          description: string | null;
          updated_at: string;
        };
        Insert: {
          key: string;
          value: any;
          description?: string | null;
          updated_at?: string;
        };
        Update: {
          key?: string;
          value?: any;
          description?: string | null;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      subscription_type: 'free' | 'pro' | 'enterprise';
      token_source: 'oauth' | 'manual' | 'auto';
    };
  };
}
