/**
 * 核心类型定义 - 确保与VSCode插件100%兼容
 */

// 数据库表类型
export interface User {
  id: string;
  email: string;
  user_token: string;
  subscription_type: 'free' | 'pro' | 'enterprise';
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface TokenPool {
  id: string;
  access_token: string;
  tenant_url: string;
  refresh_token?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
  is_healthy: boolean;
  last_used_at?: string;
  usage_count: number;
  source: 'oauth' | 'manual' | 'auto';
  error_count?: number;
  max_daily_usage?: number;
  health_check_at?: string;
  notes?: string;
  // 新增：Token分配相关字段
  allocated_to?: string;  // 分配给哪个用户
  allocated_at?: string;  // 分配时间
  allocation_expires_at?: string;  // 分配过期时间
}

export interface TokenUsage {
  id: string;
  user_id: string;
  token_id: string;
  used_at: string;
  request_count: number;
  session_id?: string;
  user_agent?: string;
  ip_address?: string;
}

// API请求/响应类型（与VSCode插件兼容）
export interface UserVerifyRequest {
  userToken: string;
}

export interface UserVerifyResponse {
  success: boolean;
  user?: {
    email: string;
    verified_at: string;
  };
  error?: string;
}

export interface AvailableTokensResponse {
  success: boolean;
  data?: TokenInfo[];
  error?: string;
}

export interface TokenInfo {
  id: string;
  accessToken: string;
  tenantURL: string;  // 使用驼峰命名兼容VSCode扩展
  use_time: number;
  created_at: string;
  updated_at: string;
}

export interface TokenUpdateRequest {
  token: string;
  user_ck: string;
}

export interface TokenUpdateResponse {
  success: boolean;
  message?: string;
  user_ck?: string;
  error?: string;
}

// OAuth相关类型
export interface OAuthAuthRequest {
  device_info?: {
    user_agent: string;
    device_id: string;
    fingerprint: string;
  };
}

export interface OAuthAuthResponse {
  authorize_url: string;
  state: string;
  code_verifier: string;
}

export interface OAuthCallbackRequest {
  code: string;
  state: string;
  tenant_url: string;
}

export interface OAuthCallbackResponse {
  status: 'success' | 'error';
  token?: string;
  tenant_url?: string;
  error?: string;
}

// 系统配置类型
export interface SystemConfig {
  key: string;
  value: any;
  description?: string;
  updated_at: string;
}

// 用户登录类型
export interface LoginRequest {
  email: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  user?: {
    id: string;
    email: string;
    subscription_type: string;
  };
  redirect?: string;
  error?: string;
}

// Token健康检查类型
export interface TokenHealthStatus {
  token_id: string;
  is_healthy: boolean;
  last_checked: string;
  error_message?: string;
  response_time?: number;
}

// 使用统计类型
export interface UsageStats {
  user_id: string;
  total_requests: number;
  tokens_used: number;
  last_activity: string;
  daily_usage: {
    date: string;
    requests: number;
  }[];
}

// API错误类型
export interface ApiError {
  error: string;
  code?: string;
  details?: any;
  redirect?: string;
}
